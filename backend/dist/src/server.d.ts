import 'express-async-errors';
declare class UniversityPortalServer {
    constructor();
    initialize(): Promise<void>;
    setupMiddleware(): void;
    setupRoutes(): void;
    setupErrorHandling(): void;
    healthCheck(req: any, res: any): Promise<void>;
    metricsEndpoint(req: any, res: any): Promise<void>;
    setupGracefulShutdown(): void;
    start(): Promise<void>;
}
declare const server: UniversityPortalServer;
export default server;
//# sourceMappingURL=server.d.ts.map