import mongoose from 'mongoose';
import config from '../config/index.js';
import { logger } from '../utils/logger.js';
class DatabaseService {
    constructor() {
        this.connection = null;
        this.isConnected = false;
    }
    async connect() {
        try {
            // Set up connection event listeners
            mongoose.connection.on('connected', () => {
                logger.info('MongoDB connected');
                this.isConnected = true;
            });
            mongoose.connection.on('error', (error) => {
                logger.error('MongoDB connection error:', error);
                this.isConnected = false;
            });
            mongoose.connection.on('disconnected', () => {
                logger.warn('MongoDB disconnected');
                this.isConnected = false;
            });
            mongoose.connection.on('reconnected', () => {
                logger.info('MongoDB reconnected');
                this.isConnected = true;
            });
            // Connect to MongoDB
            this.connection = await mongoose.connect(config.database.uri, config.database.options);
            logger.info(`MongoDB connected to: ${config.database.uri}`);
            // Set up change streams for cache invalidation
            this.setupChangeStreams();
            return this.connection;
        }
        catch (error) {
            logger.error('Failed to connect to MongoDB:', error);
            throw error;
        }
    }
    async disconnect() {
        try {
            if (this.connection) {
                await mongoose.disconnect();
                this.connection = null;
                this.isConnected = false;
                logger.info('MongoDB disconnected');
            }
        }
        catch (error) {
            logger.error('Error disconnecting from MongoDB:', error);
            throw error;
        }
    }
    async healthCheck() {
        try {
            if (!this.isConnected) {
                return { status: 'disconnected', message: 'Database not connected' };
            }
            // Ping the database
            await mongoose.connection.db.admin().ping();
            // Get connection stats
            const stats = await mongoose.connection.db.stats();
            return {
                status: 'healthy',
                message: 'Database connection is healthy',
                stats: {
                    collections: stats.collections,
                    dataSize: stats.dataSize,
                    indexSize: stats.indexSize,
                    storageSize: stats.storageSize,
                }
            };
        }
        catch (error) {
            logger.error('Database health check failed:', error);
            return { status: 'unhealthy', message: error.message };
        }
    }
    setupChangeStreams() {
        try {
            // Set up change streams for cache invalidation
            const collections = ['users', 'courses', 'grades', 'assignments', 'attendance'];
            collections.forEach(collectionName => {
                const collection = mongoose.connection.db.collection(collectionName);
                const changeStream = collection.watch([], { fullDocument: 'updateLookup' });
                changeStream.on('change', (change) => {
                    this.handleChangeStreamEvent(collectionName, change);
                });
                changeStream.on('error', (error) => {
                    logger.error(`Change stream error for ${collectionName}:`, error);
                });
            });
            logger.info('Change streams initialized for cache invalidation');
        }
        catch (error) {
            logger.error('Failed to setup change streams:', error);
        }
    }
    async handleChangeStreamEvent(collectionName, change) {
        try {
            const { redisService } = await import('./redis.js');
            // Invalidate related cache entries based on the change
            switch (collectionName) {
                case 'users':
                    if (change.documentKey) {
                        await redisService.del(`user:${change.documentKey._id}`);
                        await redisService.invalidatePattern(`permissions:${change.documentKey._id}`);
                    }
                    break;
                case 'courses':
                    if (change.documentKey) {
                        await redisService.del(`course:${change.documentKey._id}`);
                        await redisService.invalidatePattern('courses:*');
                    }
                    break;
                case 'grades':
                    if (change.documentKey) {
                        await redisService.invalidatePattern(`grades:${change.documentKey._id}`);
                        await redisService.invalidatePattern('grades:*');
                    }
                    break;
                case 'assignments':
                    if (change.documentKey) {
                        await redisService.del(`assignment:${change.documentKey._id}`);
                        await redisService.invalidatePattern('assignments:*');
                    }
                    break;
                case 'attendance':
                    if (change.documentKey) {
                        await redisService.invalidatePattern(`attendance:${change.documentKey._id}`);
                        await redisService.invalidatePattern('attendance:*');
                    }
                    break;
            }
            logger.debug(`Cache invalidated for ${collectionName} change:`, change.operationType);
        }
        catch (error) {
            logger.error('Error handling change stream event:', error);
        }
    }
    // Database utility methods
    async createIndexes() {
        try {
            const db = mongoose.connection.db;
            // User indexes
            await db.collection('users').createIndex({ email: 1, isActive: 1 });
            await db.collection('users').createIndex({ studentId: 1 }, { sparse: true });
            await db.collection('users').createIndex({ staffId: 1 }, { sparse: true });
            await db.collection('users').createIndex({ roles: 1, isActive: 1 });
            // Course indexes
            await db.collection('courses').createIndex({ code: 1, semester: 1 });
            await db.collection('courses').createIndex({ lecturer: 1, semester: 1 });
            await db.collection('courses').createIndex({ department: 1, semester: 1 });
            await db.collection('courses').createIndex({ isActive: 1, semester: 1 });
            // Enrollment indexes
            await db.collection('enrollments').createIndex({ student: 1, semester: 1 });
            await db.collection('enrollments').createIndex({ course: 1, semester: 1 });
            await db.collection('enrollments').createIndex({ student: 1, course: 1 }, { unique: true });
            // Grade indexes
            await db.collection('grades').createIndex({ student: 1, course: 1, semester: 1 });
            await db.collection('grades').createIndex({ course: 1, semester: 1 });
            await db.collection('grades').createIndex({ student: 1, semester: 1 });
            // Assignment indexes
            await db.collection('assignments').createIndex({ course: 1, dueDate: 1 });
            await db.collection('assignments').createIndex({ lecturer: 1, dueDate: 1 });
            await db.collection('assignments').createIndex({ isPublished: 1, dueDate: 1 });
            // Attendance indexes
            await db.collection('attendance').createIndex({ student: 1, course: 1, date: 1 });
            await db.collection('attendance').createIndex({ course: 1, date: 1 });
            await db.collection('attendance').createIndex({ student: 1, date: 1 });
            // Session indexes
            await db.collection('sessions').createIndex({ course: 1, date: 1 });
            await db.collection('sessions').createIndex({ lecturer: 1, date: 1 });
            await db.collection('sessions').createIndex({ date: 1, startTime: 1 });
            // Notification indexes
            await db.collection('notifications').createIndex({ user: 1, read: 1, createdAt: -1 });
            await db.collection('notifications').createIndex({ createdAt: 1 }, { expireAfterSeconds: 2592000 }); // 30 days
            logger.info('Database indexes created successfully');
        }
        catch (error) {
            logger.error('Error creating database indexes:', error);
            throw error;
        }
    }
    async getConnectionStats() {
        try {
            if (!this.isConnected) {
                return null;
            }
            const admin = mongoose.connection.db.admin();
            const serverStatus = await admin.serverStatus();
            const dbStats = await mongoose.connection.db.stats();
            return {
                version: serverStatus.version,
                uptime: serverStatus.uptime,
                connections: serverStatus.connections,
                operations: serverStatus.opcounters,
                memory: serverStatus.mem,
                database: {
                    collections: dbStats.collections,
                    dataSize: dbStats.dataSize,
                    indexSize: dbStats.indexSize,
                    storageSize: dbStats.storageSize,
                }
            };
        }
        catch (error) {
            logger.error('Error getting connection stats:', error);
            return null;
        }
    }
    // Transaction support
    async withTransaction(callback) {
        const session = await mongoose.startSession();
        try {
            await session.withTransaction(async () => {
                await callback(session);
            });
        }
        finally {
            await session.endSession();
        }
    }
    // Bulk operations
    async bulkWrite(collectionName, operations) {
        try {
            const collection = mongoose.connection.db.collection(collectionName);
            const result = await collection.bulkWrite(operations);
            return result;
        }
        catch (error) {
            logger.error(`Bulk write error for ${collectionName}:`, error);
            throw error;
        }
    }
    // Aggregation pipeline
    async aggregate(collectionName, pipeline) {
        try {
            const collection = mongoose.connection.db.collection(collectionName);
            const result = await collection.aggregate(pipeline).toArray();
            return result;
        }
        catch (error) {
            logger.error(`Aggregation error for ${collectionName}:`, error);
            throw error;
        }
    }
}
// Create singleton instance
const databaseService = new DatabaseService();
export default databaseService;
//# sourceMappingURL=database.js.map