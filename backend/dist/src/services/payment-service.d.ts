export interface PaymentConfig {
    mpesa: {
        consumerKey: string;
        consumerSecret: string;
        businessShortCode: string;
        passkey: string;
        environment: 'sandbox' | 'production';
        callbackUrl: string;
    };
    jambopay: {
        apiKey: string;
        merchantId: string;
        environment: 'sandbox' | 'production';
        callbackUrl: string;
    };
    imBank: {
        clientId: string;
        clientSecret: string;
        baseUrl: string;
        environment: 'sandbox' | 'production';
    };
    kcbBuni: {
        apiKey: string;
        merchantId: string;
        baseUrl: string;
        environment: 'sandbox' | 'production';
    };
    pesalink: {
        apiKey: string;
        merchantId: string;
        baseUrl: string;
        environment: 'sandbox' | 'production';
    };
}
export interface PaymentRequest {
    studentId: string;
    feeId: string;
    amount: number;
    phoneNumber?: string;
    accountNumber?: string;
    bankCode?: string;
    description?: string;
}
export interface PaymentResponse {
    success: boolean;
    transactionId?: string;
    referenceNumber?: string;
    message: string;
    providerResponse?: any;
    error?: string;
}
export declare class MpesaService {
    private config;
    private httpClient;
    constructor(config: PaymentConfig['mpesa']);
    initiatePayment(request: PaymentRequest): Promise<PaymentResponse>;
    verifyPayment(checkoutRequestId: string): Promise<any>;
    private getAccessToken;
    private generateTimestamp;
    private generatePassword;
    private generateReferenceNumber;
    private isValidPhoneNumber;
}
export declare class JamboPayService {
    private config;
    private httpClient;
    constructor(config: PaymentConfig['jambopay']);
    initiatePayment(request: PaymentRequest): Promise<PaymentResponse>;
    private generateReferenceNumber;
}
export declare class ImBankService {
    private config;
    private httpClient;
    constructor(config: PaymentConfig['imBank']);
    initiatePayment(request: PaymentRequest): Promise<PaymentResponse>;
    private getAccessToken;
    private generateReferenceNumber;
}
export declare class PaymentServiceManager {
    private mpesaService;
    private jambopayService;
    private imBankService;
    private config;
    constructor(config: PaymentConfig);
    initiatePayment(paymentMethod: string, request: PaymentRequest): Promise<PaymentResponse>;
    handleWebhook(provider: string, payload: any, signature: string): Promise<void>;
    private processWebhook;
    private processMpesaWebhook;
    private processJamboPayWebhook;
    private processImBankWebhook;
    private verifyWebhookSignature;
    private verifyMpesaSignature;
    private verifyJamboPaySignature;
    private verifyImBankSignature;
    private generateReferenceNumber;
}
export default PaymentServiceManager;
//# sourceMappingURL=payment-service.d.ts.map