import mongoose from 'mongoose';
declare class DatabaseService {
    constructor();
    connect(): Promise<any>;
    disconnect(): Promise<void>;
    healthCheck(): Promise<{
        status: string;
        message: string;
        stats: {
            collections: any;
            dataSize: any;
            indexSize: any;
            storageSize: any;
        };
    } | {
        status: string;
        message: any;
        stats?: undefined;
    }>;
    setupChangeStreams(): void;
    handleChangeStreamEvent(collectionName: any, change: any): Promise<void>;
    createIndexes(): Promise<void>;
    getConnectionStats(): Promise<{
        version: any;
        uptime: any;
        connections: any;
        operations: any;
        memory: any;
        database: {
            collections: any;
            dataSize: any;
            indexSize: any;
            storageSize: any;
        };
    } | null>;
    withTransaction(callback: any): Promise<void>;
    bulkWrite(collectionName: any, operations: any): Promise<mongoose.mongo.BulkWriteResult>;
    aggregate(collectionName: any, pipeline: any): Promise<mongoose.mongo.BSON.Document[]>;
}
declare const databaseService: DatabaseService;
export default databaseService;
//# sourceMappingURL=database.d.ts.map