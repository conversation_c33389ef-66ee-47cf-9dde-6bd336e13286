import { Cluster } from 'ioredis';
declare class RedisService {
    private client;
    private cluster;
    private isConnected;
    constructor();
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    getClient(): Redis | Cluster | null;
    get(key: string): Promise<any>;
    set(key: string, value: any, ttl?: number | null): Promise<boolean>;
    del(key: string): Promise<boolean>;
    exists(key: string): Promise<boolean>;
    expire(key: string, ttl: number): Promise<boolean>;
    ttl(key: string): Promise<number>;
    hget(key: string, field: string): Promise<any>;
    hset(key: string, field: string, value: any): Promise<boolean>;
    hdel(key: string, field: string): Promise<boolean>;
    hgetall(key: string): Promise<Record<string, any>>;
    lpush(key: string, ...values: any[]): Promise<number>;
    rpop(key: string): Promise<any>;
    llen(key: string): Promise<number>;
    sadd(key: string, ...members: string[]): Promise<number>;
    srem(key: string, ...members: string[]): Promise<number>;
    smembers(key: string): Promise<string[]>;
    sismember(key: string, member: string): Promise<boolean>;
    zadd(key: string, score: number, member: string): Promise<number>;
    zrange(key: string, start: number, stop: number, withScores?: boolean): Promise<string[]>;
    zrem(key: string, ...members: string[]): Promise<number>;
    publish(channel: string, message: any): Promise<number>;
    subscribe(channel: string, callback: (message: any) => void): Promise<Redis | null>;
    invalidatePattern(pattern: string): Promise<number>;
    ping(): Promise<boolean>;
    getConnectionInfo(): {
        isConnected: boolean;
        type: string;
        nodes: string[];
    };
}
declare const redisService: RedisService;
export { redisService };
export declare const getRedisClient: () => any;
export default redisService;
//# sourceMappingURL=redis.d.ts.map