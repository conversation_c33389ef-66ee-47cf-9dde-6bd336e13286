import express from 'express';
import helmet from 'helmet';
import cors from 'cors';
import compression from 'compression';
import 'express-async-errors';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import config from './config/index.js';
import databaseService from './services/database.js';
import { redisService } from './services/redis.js';
import { logger, requestLogger, errorLogger } from './utils/logger.js';
import { setupPrometheusMetrics } from './utils/metrics.js';
import { setupTracing } from './utils/tracing.js';
// Import routes
import authRoutes from './routes/auth.js';
import userRoutes from './routes/users.js';
import courseRoutes from './routes/courses.js';
import gradeRoutes from './routes/grades.js';
import assignmentRoutes from './routes/assignments.js';
import attendanceRoutes from './routes/attendance.js';
import departmentRoutes from './routes/departments.js';
import notificationRoutes from './routes/notifications.js';
import adminRoutes from './routes/admin.js';
import paymentRoutes from './routes/payments.js';
// Import middleware
import { authenticate, apiRateLimit } from './middleware/auth.js';
import { securityHeaders } from './middleware/security.js';
// Import socket handlers
import { setupSocketHandlers } from './socket/handlers.js';
class UniversityPortalServer {
    constructor() {
        this.app = express();
        this.server = createServer(this.app);
        this.io = new SocketIOServer(this.server, {
            cors: {
                origin: process.env.CLIENT_URL || "http://localhost:3000",
                methods: ["GET", "POST"],
                credentials: true
            },
            transports: ['websocket', 'polling']
        });
        this.isReady = false;
    }
    async initialize() {
        try {
            logger.info('Initializing University Portal API Server...');
            // Setup tracing first
            if (config.features.tracing) {
                await setupTracing();
                logger.info('OpenTelemetry tracing initialized');
            }
            // Setup metrics
            if (config.features.metrics) {
                await setupPrometheusMetrics();
                logger.info('Prometheus metrics initialized');
            }
            // Connect to databases
            await databaseService.connect();
            await redisService.connect();
            // Setup Redis adapter for Socket.IO
            if (config.features.realTime) {
                const pubClient = redisService.getClient();
                const subClient = pubClient.duplicate();
                this.io.adapter(createAdapter(pubClient, subClient));
                logger.info('Socket.IO Redis adapter configured');
            }
            // Setup middleware
            this.setupMiddleware();
            // Setup routes
            this.setupRoutes();
            // Setup socket handlers
            if (config.features.realTime) {
                setupSocketHandlers(this.io);
                logger.info('Socket.IO handlers configured');
            }
            // Setup error handling
            this.setupErrorHandling();
            // Setup graceful shutdown
            this.setupGracefulShutdown();
            this.isReady = true;
            logger.info('University Portal API Server initialized successfully');
        }
        catch (error) {
            logger.error('Failed to initialize server:', error);
            throw error;
        }
    }
    setupMiddleware() {
        // Trust proxy for load balancer
        if (config.server.trustProxy) {
            this.app.set('trust proxy', true);
        }
        // Security middleware
        this.app.use(helmet({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                },
            },
            crossOriginEmbedderPolicy: false,
        }));
        // CORS configuration
        this.app.use(cors({
            origin: process.env.CLIENT_URL || "http://localhost:3000",
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
        }));
        // Compression
        this.app.use(compression());
        // Body parsing
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
        // Request logging
        this.app.use(requestLogger);
        // Rate limiting
        this.app.use(apiRateLimit);
        // Security headers
        this.app.use(securityHeaders);
        // Health check endpoint
        this.app.get('/health', this.healthCheck.bind(this));
        // Metrics endpoint
        if (config.features.metrics) {
            this.app.get('/metrics', this.metricsEndpoint.bind(this));
        }
    }
    setupRoutes() {
        // API routes
        this.app.use('/api/auth', authRoutes);
        this.app.use('/api/users', authenticate, userRoutes);
        this.app.use('/api/courses', authenticate, courseRoutes);
        this.app.use('/api/grades', authenticate, gradeRoutes);
        this.app.use('/api/assignments', authenticate, assignmentRoutes);
        this.app.use('/api/attendance', authenticate, attendanceRoutes);
        this.app.use('/api/departments', authenticate, departmentRoutes);
        this.app.use('/api/notifications', authenticate, notificationRoutes);
        this.app.use('/api/admin', authenticate, adminRoutes);
        this.app.use('/api/payments', paymentRoutes);
        // 404 handler
        this.app.use('*', (req, res) => {
            res.status(404).json({
                success: false,
                message: 'Route not found',
                code: 'ROUTE_NOT_FOUND'
            });
        });
    }
    setupErrorHandling() {
        // Error logging middleware
        this.app.use(errorLogger);
        // Global error handler
        this.app.use((error, req, res, next) => {
            logger.error('Unhandled error:', error);
            // Don't leak error details in production
            const isDevelopment = config.server.env === 'development';
            res.status(error.status || 500).json({
                success: false,
                message: error.message || 'Internal server error',
                code: error.code || 'INTERNAL_ERROR',
                ...(isDevelopment && { stack: error.stack }),
            });
        });
    }
    async healthCheck(req, res) {
        try {
            const dbHealth = await databaseService.healthCheck();
            const redisHealth = await redisService.ping();
            const health = {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                version: process.env.npm_package_version || '1.0.0',
                environment: config.server.env,
                services: {
                    database: dbHealth,
                    redis: {
                        status: redisHealth ? 'healthy' : 'unhealthy',
                        connection: redisService.getConnectionInfo(),
                    },
                },
                memory: process.memoryUsage(),
                cpu: process.cpuUsage(),
            };
            const isHealthy = dbHealth.status === 'healthy' && redisHealth;
            res.status(isHealthy ? 200 : 503).json(health);
        }
        catch (error) {
            logger.error('Health check failed:', error);
            res.status(503).json({
                status: 'unhealthy',
                timestamp: new Date().toISOString(),
                error: error.message,
            });
        }
    }
    async metricsEndpoint(req, res) {
        try {
            const { register } = await import('./utils/metrics.js');
            res.set('Content-Type', register.contentType);
            res.end(await register.metrics());
        }
        catch (error) {
            logger.error('Metrics endpoint error:', error);
            res.status(500).json({ error: 'Failed to generate metrics' });
        }
    }
    setupGracefulShutdown() {
        const shutdown = async (signal) => {
            logger.info(`Received ${signal}, starting graceful shutdown...`);
            this.isReady = false;
            // Stop accepting new connections
            this.server.close(async () => {
                logger.info('HTTP server closed');
                try {
                    // Close Socket.IO server
                    this.io.close();
                    logger.info('Socket.IO server closed');
                    // Close database connections
                    await databaseService.disconnect();
                    logger.info('Database disconnected');
                    // Close Redis connections
                    await redisService.disconnect();
                    logger.info('Redis disconnected');
                    logger.info('Graceful shutdown completed');
                    process.exit(0);
                }
                catch (error) {
                    logger.error('Error during graceful shutdown:', error);
                    process.exit(1);
                }
            });
            // Force close after 30 seconds
            setTimeout(() => {
                logger.error('Forced shutdown after timeout');
                process.exit(1);
            }, 30000);
        };
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('SIGINT', () => shutdown('SIGINT'));
        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            logger.error('Uncaught exception:', error);
            shutdown('uncaughtException');
        });
        process.on('unhandledRejection', (reason, promise) => {
            logger.error('Unhandled rejection at:', promise, 'reason:', reason);
            shutdown('unhandledRejection');
        });
    }
    async start() {
        try {
            await this.initialize();
            this.server.listen(config.server.port, config.server.host, () => {
                logger.info(`University Portal API Server running on ${config.server.host}:${config.server.port}`);
                logger.info(`Environment: ${config.server.env}`);
                logger.info(`Features enabled: ${Object.entries(config.features).filter(([, enabled]) => enabled).map(([feature]) => feature).join(', ')}`);
            });
        }
        catch (error) {
            logger.error('Failed to start server:', error);
            process.exit(1);
        }
    }
}
// Create and start server
const server = new UniversityPortalServer();
server.start();
export default server;
//# sourceMappingURL=server.js.map