import express from 'express';
import { body, validationResult } from 'express-validator';
import { Attendance, Session, Course } from '../models/index.js';
import { authenticate, requireRole } from '../middleware/auth.js';
import { logger, auditLogger } from '../utils/logger.js';
import { publishRealtimeUpdate } from '../utils/realtime.js';
const router = express.Router();
// Validation rules
const sessionValidation = [
    body('course').isMongoId().withMessage('Valid course ID required'),
    body('date').isISO8601().withMessage('Valid date required'),
    body('startTime').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('Valid start time required'),
    body('endTime').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('Valid end time required'),
    body('room').optional().trim(),
    body('type').isIn(['lecture', 'lab', 'tutorial', 'seminar']).withMessage('Invalid session type'),
    body('topic').optional().trim(),
];
const attendanceValidation = [
    body('student').isMongoId().withMessage('Valid student ID required'),
    body('session').isMongoId().withMessage('Valid session ID required'),
    body('status').isIn(['present', 'absent', 'late', 'excused']).withMessage('Invalid attendance status'),
];
// GET /api/attendance - List attendance records
router.get('/', authenticate, async (req, res) => {
    try {
        const { page = 1, limit = 20, student, course, session, date, status, sortBy = 'date', sortOrder = 'desc' } = req.query;
        // Build filter query based on user role
        const filter = {};
        if (req.user.roles.includes('student')) {
            // Students can only see their own attendance
            filter.student = req.user._id;
        }
        else if (req.user.roles.includes('lecturer')) {
            // Lecturers can see attendance for their courses
            const lecturerCourses = await Course.find({ lecturer: req.user._id }).select('_id');
            const courseIds = lecturerCourses.map(c => c._id);
            // Get sessions for lecturer's courses
            const sessions = await Session.find({ course: { $in: courseIds } }).select('_id');
            const sessionIds = sessions.map(s => s._id);
            filter.session = { $in: sessionIds };
        }
        // Apply additional filters
        if (student && req.user.roles.includes('lecturer'))
            filter.student = student;
        if (course) {
            const sessions = await Session.find({ course }).select('_id');
            filter.session = { $in: sessions.map(s => s._id) };
        }
        if (session)
            filter.session = session;
        if (date)
            filter.date = new Date(date);
        if (status)
            filter.status = status;
        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
        // Execute query with pagination and population
        const attendance = await Attendance.find(filter)
            .populate('student', 'name email studentId')
            .populate('session', 'course date startTime endTime room type topic')
            .populate('markedBy', 'name email')
            .sort(sort)
            .skip(skip)
            .limit(parseInt(limit));
        const total = await Attendance.countDocuments(filter);
        res.json({
            success: true,
            data: {
                attendance,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total,
                    pages: Math.ceil(total / parseInt(limit))
                }
            }
        });
    }
    catch (error) {
        logger.error('Error fetching attendance:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch attendance',
            code: 'FETCH_ATTENDANCE_ERROR'
        });
    }
});
// GET /api/attendance/:id - Get attendance record by ID
router.get('/:id', authenticate, async (req, res) => {
    try {
        const { id } = req.params;
        const attendance = await Attendance.findById(id)
            .populate('student', 'name email studentId')
            .populate('session', 'course date startTime endTime room type topic')
            .populate('markedBy', 'name email');
        if (!attendance) {
            return res.status(404).json({
                success: false,
                message: 'Attendance record not found',
                code: 'ATTENDANCE_NOT_FOUND'
            });
        }
        // Check permissions
        if (req.user.roles.includes('student') && attendance.student._id.toString() !== req.user._id) {
            return res.status(403).json({
                success: false,
                message: 'Access denied',
                code: 'ACCESS_DENIED'
            });
        }
        res.json({
            success: true,
            data: { attendance }
        });
    }
    catch (error) {
        logger.error('Error fetching attendance:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch attendance',
            code: 'FETCH_ATTENDANCE_ERROR'
        });
    }
});
// POST /api/attendance - Mark attendance (Lecturer/Admin only)
router.post('/', authenticate, requireRole('lecturer', 'admin'), attendanceValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array(),
                code: 'VALIDATION_ERROR'
            });
        }
        const { student, session, status, notes } = req.body;
        // Verify session exists
        const sessionObj = await Session.findById(session);
        if (!sessionObj) {
            return res.status(400).json({
                success: false,
                message: 'Invalid session',
                code: 'INVALID_SESSION'
            });
        }
        // Check if lecturer can mark attendance for this session
        if (req.user.roles.includes('lecturer')) {
            const course = await Course.findById(sessionObj.course);
            if (!course || course.lecturer.toString() !== req.user._id) {
                return res.status(403).json({
                    success: false,
                    message: 'You can only mark attendance for your own courses',
                    code: 'INSUFFICIENT_PERMISSION'
                });
            }
        }
        // Check if attendance already exists
        const existingAttendance = await Attendance.findOne({
            student,
            session
        });
        if (existingAttendance) {
            return res.status(409).json({
                success: false,
                message: 'Attendance already marked for this session',
                code: 'ATTENDANCE_EXISTS'
            });
        }
        // Create attendance record
        const attendance = new Attendance({
            student,
            session,
            date: sessionObj.date,
            status,
            markedBy: req.user._id,
            markedAt: new Date(),
            notes
        });
        await attendance.save();
        // Populate attendance for response
        await attendance.populate([
            { path: 'student', select: 'name email studentId' },
            { path: 'session', select: 'course date startTime endTime room type topic' },
            { path: 'markedBy', select: 'name email' }
        ]);
        // Publish real-time update
        await publishRealtimeUpdate('attendance_marked', {
            attendance: {
                id: attendance._id,
                studentId: student,
                sessionId: session,
                courseId: sessionObj.course,
                status: status,
                date: sessionObj.date
            },
            markedBy: req.user._id
        });
        // Log attendance marking
        auditLogger('attendance_marked', 'attendance', req.user._id, {
            attendanceId: attendance._id,
            studentId: student,
            sessionId: session,
            status: status
        });
        res.status(201).json({
            success: true,
            message: 'Attendance marked successfully',
            data: { attendance }
        });
    }
    catch (error) {
        logger.error('Error marking attendance:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to mark attendance',
            code: 'MARK_ATTENDANCE_ERROR'
        });
    }
});
// PUT /api/attendance/:id - Update attendance record
router.put('/:id', authenticate, requireRole('lecturer', 'admin'), attendanceValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array(),
                code: 'VALIDATION_ERROR'
            });
        }
        const { id } = req.params;
        const { status, notes } = req.body;
        const attendance = await Attendance.findById(id);
        if (!attendance) {
            return res.status(404).json({
                success: false,
                message: 'Attendance record not found',
                code: 'ATTENDANCE_NOT_FOUND'
            });
        }
        // Check permissions
        if (req.user.roles.includes('lecturer')) {
            const session = await Session.findById(attendance.session);
            if (session) {
                const course = await Course.findById(session.course);
                if (!course || course.lecturer.toString() !== req.user._id) {
                    return res.status(403).json({
                        success: false,
                        message: 'You can only modify attendance for your own courses',
                        code: 'INSUFFICIENT_PERMISSION'
                    });
                }
            }
        }
        // Update attendance fields
        attendance.status = status;
        attendance.notes = notes;
        attendance.markedAt = new Date();
        await attendance.save();
        // Populate attendance for response
        await attendance.populate([
            { path: 'student', select: 'name email studentId' },
            { path: 'session', select: 'course date startTime endTime room type topic' },
            { path: 'markedBy', select: 'name email' }
        ]);
        // Publish real-time update
        await publishRealtimeUpdate('attendance_updated', {
            attendance: {
                id: attendance._id,
                studentId: attendance.student._id,
                sessionId: attendance.session._id,
                courseId: attendance.session.course,
                status: status
            },
            updatedBy: req.user._id
        });
        // Log update
        auditLogger('attendance_updated', 'attendance', req.user._id, {
            attendanceId: attendance._id,
            studentId: attendance.student._id,
            changes: { status, notes }
        });
        res.json({
            success: true,
            message: 'Attendance updated successfully',
            data: { attendance }
        });
    }
    catch (error) {
        logger.error('Error updating attendance:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update attendance',
            code: 'UPDATE_ATTENDANCE_ERROR'
        });
    }
});
// DELETE /api/attendance/:id - Delete attendance record
router.delete('/:id', authenticate, requireRole('lecturer', 'admin'), async (req, res) => {
    try {
        const { id } = req.params;
        const attendance = await Attendance.findById(id);
        if (!attendance) {
            return res.status(404).json({
                success: false,
                message: 'Attendance record not found',
                code: 'ATTENDANCE_NOT_FOUND'
            });
        }
        // Check permissions
        if (req.user.roles.includes('lecturer')) {
            const session = await Session.findById(attendance.session);
            if (session) {
                const course = await Course.findById(session.course);
                if (!course || course.lecturer.toString() !== req.user._id) {
                    return res.status(403).json({
                        success: false,
                        message: 'You can only delete attendance for your own courses',
                        code: 'INSUFFICIENT_PERMISSION'
                    });
                }
            }
        }
        await Attendance.findByIdAndDelete(id);
        // Publish real-time update
        await publishRealtimeUpdate('attendance_deleted', {
            attendanceId: attendance._id,
            studentId: attendance.student,
            sessionId: attendance.session,
            deletedBy: req.user._id
        });
        // Log deletion
        auditLogger('attendance_deleted', 'attendance', req.user._id, {
            attendanceId: attendance._id,
            studentId: attendance.student,
            sessionId: attendance.session
        });
        res.json({
            success: true,
            message: 'Attendance record deleted successfully'
        });
    }
    catch (error) {
        logger.error('Error deleting attendance:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete attendance',
            code: 'DELETE_ATTENDANCE_ERROR'
        });
    }
});
// GET /api/attendance/session/:sessionId - Get attendance for a session
router.get('/session/:sessionId', authenticate, requireRole('lecturer', 'admin'), async (req, res) => {
    try {
        const { sessionId } = req.params;
        const session = await Session.findById(sessionId);
        if (!session) {
            return res.status(404).json({
                success: false,
                message: 'Session not found',
                code: 'SESSION_NOT_FOUND'
            });
        }
        // Check permissions
        if (req.user.roles.includes('lecturer')) {
            const course = await Course.findById(session.course);
            if (!course || course.lecturer.toString() !== req.user._id) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to this session',
                    code: 'ACCESS_DENIED'
                });
            }
        }
        const attendance = await Attendance.find({ session: sessionId })
            .populate('student', 'name email studentId')
            .populate('markedBy', 'name email')
            .sort({ student: 1 });
        res.json({
            success: true,
            data: {
                session,
                attendance
            }
        });
    }
    catch (error) {
        logger.error('Error fetching session attendance:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch session attendance',
            code: 'FETCH_SESSION_ATTENDANCE_ERROR'
        });
    }
});
// GET /api/attendance/student/:studentId - Get attendance for a student
router.get('/student/:studentId', authenticate, async (req, res) => {
    try {
        const { studentId } = req.params;
        // Check permissions
        if (req.user.roles.includes('student') && studentId !== req.user._id) {
            return res.status(403).json({
                success: false,
                message: 'Access denied',
                code: 'ACCESS_DENIED'
            });
        }
        const attendance = await Attendance.find({ student: studentId })
            .populate('session', 'course date startTime endTime room type topic')
            .populate('markedBy', 'name email')
            .sort({ date: -1 });
        res.json({
            success: true,
            data: { attendance }
        });
    }
    catch (error) {
        logger.error('Error fetching student attendance:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch student attendance',
            code: 'FETCH_STUDENT_ATTENDANCE_ERROR'
        });
    }
});
// POST /api/attendance/bulk - Bulk mark attendance (Lecturer/Admin only)
router.post('/bulk', authenticate, requireRole('lecturer', 'admin'), async (req, res) => {
    try {
        const { sessionId, attendanceRecords } = req.body;
        if (!Array.isArray(attendanceRecords)) {
            return res.status(400).json({
                success: false,
                message: 'Attendance records must be an array',
                code: 'INVALID_DATA'
            });
        }
        const session = await Session.findById(sessionId);
        if (!session) {
            return res.status(400).json({
                success: false,
                message: 'Invalid session',
                code: 'INVALID_SESSION'
            });
        }
        // Check permissions
        if (req.user.roles.includes('lecturer')) {
            const course = await Course.findById(session.course);
            if (!course || course.lecturer.toString() !== req.user._id) {
                return res.status(403).json({
                    success: false,
                    message: 'You can only mark attendance for your own courses',
                    code: 'INSUFFICIENT_PERMISSION'
                });
            }
        }
        const createdAttendance = [];
        const errors = [];
        for (const record of attendanceRecords) {
            try {
                const { studentId, status, notes } = record;
                // Check if attendance already exists
                const existingAttendance = await Attendance.findOne({
                    student: studentId,
                    session: sessionId
                });
                if (existingAttendance) {
                    errors.push({
                        studentId,
                        error: 'Attendance already marked for this session'
                    });
                    continue;
                }
                // Create attendance record
                const attendance = new Attendance({
                    student: studentId,
                    session: sessionId,
                    date: session.date,
                    status,
                    markedBy: req.user._id,
                    markedAt: new Date(),
                    notes
                });
                await attendance.save();
                createdAttendance.push(attendance);
                // Publish real-time update
                await publishRealtimeUpdate('attendance_marked', {
                    attendance: {
                        id: attendance._id,
                        studentId: studentId,
                        sessionId: sessionId,
                        courseId: session.course,
                        status: status,
                        date: session.date
                    },
                    markedBy: req.user._id
                });
            }
            catch (error) {
                errors.push({
                    studentId: record.studentId,
                    error: error.message
                });
            }
        }
        // Log bulk attendance marking
        auditLogger('bulk_attendance_marked', 'attendance', req.user._id, {
            sessionId: sessionId,
            recordsProcessed: attendanceRecords.length,
            recordsCreated: createdAttendance.length,
            errors: errors.length
        });
        res.status(201).json({
            success: true,
            message: `Bulk attendance marked: ${createdAttendance.length} records created`,
            data: {
                created: createdAttendance.length,
                errors: errors.length,
                details: errors
            }
        });
    }
    catch (error) {
        logger.error('Error bulk marking attendance:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to bulk mark attendance',
            code: 'BULK_MARK_ATTENDANCE_ERROR'
        });
    }
});
export default router;
//# sourceMappingURL=attendance.js.map