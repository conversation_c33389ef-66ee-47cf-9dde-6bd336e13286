import express from 'express';
import { body, validationResult } from 'express-validator';
import { Course, User, Enrollment } from '../models/index.js';
import { authenticate, requireRole } from '../middleware/auth.js';
import { logger, auditLogger } from '../utils/logger.js';
import { publishRealtimeUpdate } from '../utils/realtime.js';
const router = express.Router();
// Validation rules
const courseValidation = [
    body('code').trim().isLength({ min: 3 }).withMessage('Course code must be at least 3 characters'),
    body('title').trim().isLength({ min: 5 }).withMessage('Course title must be at least 5 characters'),
    body('description').optional().trim(),
    body('credits').isInt({ min: 1, max: 6 }).withMessage('Credits must be between 1 and 6'),
    body('lecturer').isMongoId().withMessage('Valid lecturer ID required'),
    body('department').trim().notEmpty().withMessage('Department is required'),
    body('capacity').isInt({ min: 1, max: 200 }).withMessage('Capacity must be between 1 and 200'),
    body('prerequisites').optional().isArray(),
    body('semester').trim().notEmpty().withMessage('Semester is required'),
    body('academicYear').trim().notEmpty().withMessage('Academic year is required'),
];
// GET /api/courses - List courses with pagination and filtering
router.get('/', authenticate, async (req, res) => {
    try {
        const { page = 1, limit = 20, department, lecturer, semester, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        // Build filter query
        const filter = { isActive: true };
        if (department)
            filter.department = department;
        if (lecturer)
            filter.lecturer = lecturer;
        if (semester)
            filter.semester = semester;
        if (search) {
            filter.$or = [
                { code: { $regex: search, $options: 'i' } },
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } }
            ];
        }
        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
        // Execute query with pagination and population
        const courses = await Course.find(filter)
            .populate('lecturer', 'name email staffId')
            .sort(sort)
            .skip(skip)
            .limit(parseInt(limit));
        const total = await Course.countDocuments(filter);
        res.json({
            success: true,
            data: {
                courses,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total,
                    pages: Math.ceil(total / parseInt(limit))
                }
            }
        });
    }
    catch (error) {
        logger.error('Error fetching courses:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch courses',
            code: 'FETCH_COURSES_ERROR'
        });
    }
});
// GET /api/courses/:id - Get course by ID
router.get('/:id', authenticate, async (req, res) => {
    try {
        const { id } = req.params;
        const course = await Course.findById(id)
            .populate('lecturer', 'name email staffId department')
            .populate('prerequisites', 'code title');
        if (!course || !course.isActive) {
            return res.status(404).json({
                success: false,
                message: 'Course not found',
                code: 'COURSE_NOT_FOUND'
            });
        }
        // Get enrollment count
        const enrollmentCount = await Enrollment.countDocuments({
            course: id,
            status: 'enrolled'
        });
        res.json({
            success: true,
            data: {
                course: {
                    ...course.toObject(),
                    enrollmentCount
                }
            }
        });
    }
    catch (error) {
        logger.error('Error fetching course:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch course',
            code: 'FETCH_COURSE_ERROR'
        });
    }
});
// POST /api/courses - Create new course (Admin/Lecturer only)
router.post('/', authenticate, requireRole('admin', 'lecturer'), courseValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array(),
                code: 'VALIDATION_ERROR'
            });
        }
        const { code, title, description, credits, lecturer, department, capacity, prerequisites, semester, academicYear, schedule } = req.body;
        // Check if course code already exists for this semester
        const existingCourse = await Course.findOne({
            code,
            semester,
            academicYear,
            isActive: true
        });
        if (existingCourse) {
            return res.status(409).json({
                success: false,
                message: 'Course with this code already exists for this semester',
                code: 'COURSE_EXISTS'
            });
        }
        // Verify lecturer exists and has lecturer role
        const lecturerUser = await User.findOne({
            _id: lecturer,
            roles: 'lecturer',
            isActive: true
        });
        if (!lecturerUser) {
            return res.status(400).json({
                success: false,
                message: 'Invalid lecturer',
                code: 'INVALID_LECTURER'
            });
        }
        // Create new course
        const course = new Course({
            code,
            title,
            description,
            credits,
            lecturer,
            department,
            capacity,
            prerequisites: prerequisites || [],
            semester,
            academicYear,
            schedule: schedule || [],
            enrolled: 0
        });
        await course.save();
        // Populate lecturer info for response
        await course.populate('lecturer', 'name email staffId');
        // Publish real-time update
        await publishRealtimeUpdate('course_created', {
            course: {
                id: course._id,
                code: course.code,
                title: course.title,
                department: course.department,
                lecturer: course.lecturer.name
            },
            createdBy: req.user._id
        });
        // Log creation
        auditLogger('course_created', 'course', req.user._id, {
            courseId: course._id,
            code: course.code,
            title: course.title,
            lecturer: lecturerUser.name
        });
        res.status(201).json({
            success: true,
            message: 'Course created successfully',
            data: { course }
        });
    }
    catch (error) {
        logger.error('Error creating course:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create course',
            code: 'CREATE_COURSE_ERROR'
        });
    }
});
// PUT /api/courses/:id - Update course
router.put('/:id', authenticate, requireRole('admin', 'lecturer'), courseValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array(),
                code: 'VALIDATION_ERROR'
            });
        }
        const { id } = req.params;
        const updateData = req.body;
        const course = await Course.findById(id);
        if (!course || !course.isActive) {
            return res.status(404).json({
                success: false,
                message: 'Course not found',
                code: 'COURSE_NOT_FOUND'
            });
        }
        // Check if lecturer can modify this course
        if (req.user.roles.includes('lecturer') && course.lecturer.toString() !== req.user._id) {
            return res.status(403).json({
                success: false,
                message: 'You can only modify your own courses',
                code: 'INSUFFICIENT_PERMISSION'
            });
        }
        // Update course fields
        Object.assign(course, updateData);
        course.updatedAt = new Date();
        await course.save();
        await course.populate('lecturer', 'name email staffId');
        // Publish real-time update
        await publishRealtimeUpdate('course_updated', {
            course: {
                id: course._id,
                code: course.code,
                title: course.title,
                department: course.department
            },
            updatedBy: req.user._id
        });
        // Log update
        auditLogger('course_updated', 'course', req.user._id, {
            courseId: course._id,
            code: course.code,
            changes: updateData
        });
        res.json({
            success: true,
            message: 'Course updated successfully',
            data: { course }
        });
    }
    catch (error) {
        logger.error('Error updating course:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update course',
            code: 'UPDATE_COURSE_ERROR'
        });
    }
});
// DELETE /api/courses/:id - Delete course (Admin only)
router.delete('/:id', authenticate, requireRole('admin'), async (req, res) => {
    try {
        const { id } = req.params;
        const course = await Course.findById(id);
        if (!course || !course.isActive) {
            return res.status(404).json({
                success: false,
                message: 'Course not found',
                code: 'COURSE_NOT_FOUND'
            });
        }
        // Check if course has enrollments
        const enrollmentCount = await Enrollment.countDocuments({
            course: id,
            status: 'enrolled'
        });
        if (enrollmentCount > 0) {
            return res.status(400).json({
                success: false,
                message: 'Cannot delete course with active enrollments',
                code: 'COURSE_HAS_ENROLLMENTS'
            });
        }
        // Soft delete - mark as inactive
        course.isActive = false;
        await course.save();
        // Publish real-time update
        await publishRealtimeUpdate('course_deleted', {
            courseId: course._id,
            code: course.code,
            title: course.title,
            deletedBy: req.user._id
        });
        // Log deletion
        auditLogger('course_deleted', 'course', req.user._id, {
            courseId: course._id,
            code: course.code,
            title: course.title
        });
        res.json({
            success: true,
            message: 'Course deleted successfully'
        });
    }
    catch (error) {
        logger.error('Error deleting course:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete course',
            code: 'DELETE_COURSE_ERROR'
        });
    }
});
// POST /api/courses/:id/enroll - Enroll in course (Student only)
router.post('/:id/enroll', authenticate, requireRole('student'), async (req, res) => {
    try {
        const { id } = req.params;
        const studentId = req.user._id;
        const course = await Course.findById(id);
        if (!course || !course.isActive) {
            return res.status(404).json({
                success: false,
                message: 'Course not found',
                code: 'COURSE_NOT_FOUND'
            });
        }
        // Check if already enrolled
        const existingEnrollment = await Enrollment.findOne({
            student: studentId,
            course: id,
            status: 'enrolled'
        });
        if (existingEnrollment) {
            return res.status(409).json({
                success: false,
                message: 'Already enrolled in this course',
                code: 'ALREADY_ENROLLED'
            });
        }
        // Check capacity
        if (course.enrolled >= course.capacity) {
            return res.status(400).json({
                success: false,
                message: 'Course is full',
                code: 'COURSE_FULL'
            });
        }
        // Create enrollment
        const enrollment = new Enrollment({
            student: studentId,
            course: id,
            semester: course.semester,
            academicYear: course.academicYear,
            status: 'enrolled'
        });
        await enrollment.save();
        // Update course enrollment count
        course.enrolled += 1;
        await course.save();
        // Publish real-time update
        await publishRealtimeUpdate('course_enrolled', {
            courseId: course._id,
            courseCode: course.code,
            studentId: studentId,
            studentName: req.user.name
        });
        res.status(201).json({
            success: true,
            message: 'Successfully enrolled in course',
            data: { enrollment }
        });
    }
    catch (error) {
        logger.error('Error enrolling in course:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to enroll in course',
            code: 'ENROLLMENT_ERROR'
        });
    }
});
// DELETE /api/courses/:id/enroll - Drop course (Student only)
router.delete('/:id/enroll', authenticate, requireRole('student'), async (req, res) => {
    try {
        const { id } = req.params;
        const studentId = req.user._id;
        const enrollment = await Enrollment.findOne({
            student: studentId,
            course: id,
            status: 'enrolled'
        });
        if (!enrollment) {
            return res.status(404).json({
                success: false,
                message: 'Not enrolled in this course',
                code: 'NOT_ENROLLED'
            });
        }
        // Update enrollment status
        enrollment.status = 'dropped';
        await enrollment.save();
        // Update course enrollment count
        const course = await Course.findById(id);
        if (course) {
            course.enrolled = Math.max(0, course.enrolled - 1);
            await course.save();
        }
        // Publish real-time update
        await publishRealtimeUpdate('course_dropped', {
            courseId: id,
            studentId: studentId,
            studentName: req.user.name
        });
        res.json({
            success: true,
            message: 'Successfully dropped course'
        });
    }
    catch (error) {
        logger.error('Error dropping course:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to drop course',
            code: 'DROP_COURSE_ERROR'
        });
    }
});
export default router;
//# sourceMappingURL=courses.js.map