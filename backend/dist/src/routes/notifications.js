import express from 'express';
import { body, validationResult } from 'express-validator';
import { Notification, User } from '../models/index.js';
import { authenticate, requireRole } from '../middleware/auth.js';
import { logger, auditLogger } from '../utils/logger.js';
import { publishRealtimeUpdate } from '../utils/realtime.js';
const router = express.Router();
// Validation rules
const notificationValidation = [
    body('title').trim().isLength({ min: 3 }).withMessage('Title must be at least 3 characters'),
    body('message').trim().isLength({ min: 5 }).withMessage('Message must be at least 5 characters'),
    body('type').isIn(['info', 'success', 'warning', 'error']).withMessage('Invalid notification type'),
    body('link').optional().isURL().withMessage('Invalid link URL'),
];
// GET /api/notifications - List notifications for current user
router.get('/', authenticate, async (req, res) => {
    try {
        const { page = 1, limit = 20, type, read, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        // Build filter query
        const filter = { user: req.user._id };
        if (type)
            filter.type = type;
        if (read !== undefined)
            filter.read = read === 'true';
        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
        // Execute query with pagination
        const notifications = await Notification.find(filter)
            .sort(sort)
            .skip(skip)
            .limit(parseInt(limit));
        const total = await Notification.countDocuments(filter);
        res.json({
            success: true,
            data: {
                notifications,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total,
                    pages: Math.ceil(total / parseInt(limit))
                }
            }
        });
    }
    catch (error) {
        logger.error('Error fetching notifications:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch notifications',
            code: 'FETCH_NOTIFICATIONS_ERROR'
        });
    }
});
// GET /api/notifications/unread - Get unread notification count
router.get('/unread', authenticate, async (req, res) => {
    try {
        const unreadCount = await Notification.countDocuments({
            user: req.user._id,
            read: false
        });
        res.json({
            success: true,
            data: { unreadCount }
        });
    }
    catch (error) {
        logger.error('Error fetching unread count:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch unread count',
            code: 'FETCH_UNREAD_COUNT_ERROR'
        });
    }
});
// GET /api/notifications/:id - Get notification by ID
router.get('/:id', authenticate, async (req, res) => {
    try {
        const { id } = req.params;
        const notification = await Notification.findById(id);
        if (!notification) {
            return res.status(404).json({
                success: false,
                message: 'Notification not found',
                code: 'NOTIFICATION_NOT_FOUND'
            });
        }
        // Check if user owns this notification
        if (notification.user.toString() !== req.user._id) {
            return res.status(403).json({
                success: false,
                message: 'Access denied',
                code: 'ACCESS_DENIED'
            });
        }
        res.json({
            success: true,
            data: { notification }
        });
    }
    catch (error) {
        logger.error('Error fetching notification:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch notification',
            code: 'FETCH_NOTIFICATION_ERROR'
        });
    }
});
// POST /api/notifications - Create notification (Admin/Lecturer only)
router.post('/', authenticate, requireRole('admin', 'lecturer'), notificationValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array(),
                code: 'VALIDATION_ERROR'
            });
        }
        const { title, message, type, link, targetUsers, targetRoles, targetDepartments } = req.body;
        let notifications = [];
        // Determine target users
        if (targetUsers && Array.isArray(targetUsers)) {
            // Send to specific users
            for (const userId of targetUsers) {
                const notification = new Notification({
                    user: userId,
                    title,
                    message,
                    type,
                    link,
                    metadata: {
                        sentBy: req.user._id,
                        sentAt: new Date()
                    }
                });
                notifications.push(notification);
            }
        }
        else if (targetRoles && Array.isArray(targetRoles)) {
            // Send to users with specific roles
            const users = await User.find({
                roles: { $in: targetRoles },
                isActive: true
            }).select('_id');
            for (const user of users) {
                const notification = new Notification({
                    user: user._id,
                    title,
                    message,
                    type,
                    link,
                    metadata: {
                        sentBy: req.user._id,
                        sentAt: new Date(),
                        targetRoles
                    }
                });
                notifications.push(notification);
            }
        }
        else if (targetDepartments && Array.isArray(targetDepartments)) {
            // Send to users in specific departments
            const users = await User.find({
                department: { $in: targetDepartments },
                isActive: true
            }).select('_id');
            for (const user of users) {
                const notification = new Notification({
                    user: user._id,
                    title,
                    message,
                    type,
                    link,
                    metadata: {
                        sentBy: req.user._id,
                        sentAt: new Date(),
                        targetDepartments
                    }
                });
                notifications.push(notification);
            }
        }
        else {
            // Send to all users (admin only)
            if (!req.user.roles.includes('admin')) {
                return res.status(403).json({
                    success: false,
                    message: 'Only admins can send notifications to all users',
                    code: 'INSUFFICIENT_PERMISSION'
                });
            }
            const users = await User.find({ isActive: true }).select('_id');
            for (const user of users) {
                const notification = new Notification({
                    user: user._id,
                    title,
                    message,
                    type,
                    link,
                    metadata: {
                        sentBy: req.user._id,
                        sentAt: new Date(),
                        broadcast: true
                    }
                });
                notifications.push(notification);
            }
        }
        // Save all notifications
        const savedNotifications = await Notification.insertMany(notifications);
        // Publish real-time updates
        for (const notification of savedNotifications) {
            await publishRealtimeUpdate('notification_created', {
                notification: {
                    id: notification._id,
                    userId: notification.user,
                    title: notification.title,
                    type: notification.type
                },
                sentBy: req.user._id
            });
        }
        // Log creation
        auditLogger('notifications_created', 'notification', req.user._id, {
            count: savedNotifications.length,
            title,
            type,
            targetUsers: targetUsers?.length,
            targetRoles: targetRoles?.length,
            targetDepartments: targetDepartments?.length
        });
        res.status(201).json({
            success: true,
            message: `${savedNotifications.length} notifications created successfully`,
            data: {
                count: savedNotifications.length,
                notifications: savedNotifications
            }
        });
    }
    catch (error) {
        logger.error('Error creating notifications:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create notifications',
            code: 'CREATE_NOTIFICATIONS_ERROR'
        });
    }
});
// PUT /api/notifications/:id/read - Mark notification as read
router.put('/:id/read', authenticate, async (req, res) => {
    try {
        const { id } = req.params;
        const notification = await Notification.findById(id);
        if (!notification) {
            return res.status(404).json({
                success: false,
                message: 'Notification not found',
                code: 'NOTIFICATION_NOT_FOUND'
            });
        }
        // Check if user owns this notification
        if (notification.user.toString() !== req.user._id) {
            return res.status(403).json({
                success: false,
                message: 'Access denied',
                code: 'ACCESS_DENIED'
            });
        }
        // Mark as read
        notification.read = true;
        await notification.save();
        // Publish real-time update
        await publishRealtimeUpdate('notification_read', {
            notificationId: notification._id,
            userId: notification.user
        });
        res.json({
            success: true,
            message: 'Notification marked as read',
            data: { notification }
        });
    }
    catch (error) {
        logger.error('Error marking notification as read:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to mark notification as read',
            code: 'MARK_NOTIFICATION_READ_ERROR'
        });
    }
});
// PUT /api/notifications/read-all - Mark all notifications as read
router.put('/read-all', authenticate, async (req, res) => {
    try {
        const result = await Notification.updateMany({ user: req.user._id, read: false }, { read: true });
        // Publish real-time update
        await publishRealtimeUpdate('notifications_read_all', {
            userId: req.user._id,
            count: result.modifiedCount
        });
        res.json({
            success: true,
            message: `${result.modifiedCount} notifications marked as read`,
            data: { count: result.modifiedCount }
        });
    }
    catch (error) {
        logger.error('Error marking all notifications as read:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to mark all notifications as read',
            code: 'MARK_ALL_NOTIFICATIONS_READ_ERROR'
        });
    }
});
// DELETE /api/notifications/:id - Delete notification
router.delete('/:id', authenticate, async (req, res) => {
    try {
        const { id } = req.params;
        const notification = await Notification.findById(id);
        if (!notification) {
            return res.status(404).json({
                success: false,
                message: 'Notification not found',
                code: 'NOTIFICATION_NOT_FOUND'
            });
        }
        // Check if user owns this notification
        if (notification.user.toString() !== req.user._id) {
            return res.status(403).json({
                success: false,
                message: 'Access denied',
                code: 'ACCESS_DENIED'
            });
        }
        await Notification.findByIdAndDelete(id);
        res.json({
            success: true,
            message: 'Notification deleted successfully'
        });
    }
    catch (error) {
        logger.error('Error deleting notification:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete notification',
            code: 'DELETE_NOTIFICATION_ERROR'
        });
    }
});
// DELETE /api/notifications - Delete all notifications for user
router.delete('/', authenticate, async (req, res) => {
    try {
        const result = await Notification.deleteMany({ user: req.user._id });
        res.json({
            success: true,
            message: `${result.deletedCount} notifications deleted`,
            data: { count: result.deletedCount }
        });
    }
    catch (error) {
        logger.error('Error deleting all notifications:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete notifications',
            code: 'DELETE_NOTIFICATIONS_ERROR'
        });
    }
});
// GET /api/notifications/stats - Get notification statistics (Admin only)
router.get('/stats', authenticate, requireRole('admin'), async (req, res) => {
    try {
        const { period = '7d' } = req.query;
        // Calculate date range
        const now = new Date();
        let startDate;
        switch (period) {
            case '1d':
                startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            case '7d':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30d':
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            default:
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        }
        const stats = {
            total: await Notification.countDocuments({
                createdAt: { $gte: startDate }
            }),
            unread: await Notification.countDocuments({
                createdAt: { $gte: startDate },
                read: false
            }),
            byType: await Notification.aggregate([
                {
                    $match: {
                        createdAt: { $gte: startDate }
                    }
                },
                {
                    $group: {
                        _id: '$type',
                        count: { $sum: 1 }
                    }
                }
            ]),
            byDay: await Notification.aggregate([
                {
                    $match: {
                        createdAt: { $gte: startDate }
                    }
                },
                {
                    $group: {
                        _id: {
                            $dateToString: {
                                format: '%Y-%m-%d',
                                date: '$createdAt'
                            }
                        },
                        count: { $sum: 1 }
                    }
                },
                {
                    $sort: { _id: 1 }
                }
            ])
        };
        res.json({
            success: true,
            data: { stats }
        });
    }
    catch (error) {
        logger.error('Error fetching notification stats:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch notification statistics',
            code: 'FETCH_NOTIFICATION_STATS_ERROR'
        });
    }
});
export default router;
//# sourceMappingURL=notifications.js.map