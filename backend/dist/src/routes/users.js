import express from 'express';
import { body, validationResult } from 'express-validator';
import { User } from '../models/index.js';
import { authenticate, requireRole, requireOwnership } from '../middleware/auth.js';
import { logger, auditLogger } from '../utils/logger.js';
import { publishRealtimeUpdate } from '../utils/realtime.js';
const router = express.Router();
// Validation rules
const userValidation = [
    body('email').isEmail().normalizeEmail(),
    body('name').trim().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
    body('roles').isArray().withMessage('Roles must be an array'),
    body('roles.*').isIn(['student', 'lecturer', 'admin', 'finance', 'registrar']),
];
const courseValidation = [
    body('code').trim().isLength({ min: 3 }).withMessage('Course code must be at least 3 characters'),
    body('title').trim().isLength({ min: 5 }).withMessage('Course title must be at least 5 characters'),
    body('credits').isInt({ min: 1, max: 6 }).withMessage('Credits must be between 1 and 6'),
    body('lecturer').isMongoId().withMessage('Valid lecturer ID required'),
    body('department').trim().notEmpty().withMessage('Department is required'),
    body('capacity').isInt({ min: 1, max: 200 }).withMessage('Capacity must be between 1 and 200'),
];
const gradeValidation = [
    body('student').isMongoId().withMessage('Valid student ID required'),
    body('course').isMongoId().withMessage('Valid course ID required'),
    body('score').isFloat({ min: 0 }).withMessage('Score must be a positive number'),
    body('maxScore').isFloat({ min: 1 }).withMessage('Max score must be at least 1'),
    body('type').isIn(['assignment', 'quiz', 'exam', 'project', 'participation']).withMessage('Invalid grade type'),
];
// GET /api/users - List users with pagination and filtering
router.get('/', authenticate, requireRole('admin'), async (req, res) => {
    try {
        const { page = 1, limit = 20, role, department, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        // Build filter query
        const filter = {};
        if (role)
            filter.roles = role;
        if (department)
            filter.department = department;
        if (search) {
            filter.$or = [
                { name: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } },
                { studentId: { $regex: search, $options: 'i' } },
                { staffId: { $regex: search, $options: 'i' } }
            ];
        }
        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
        // Execute query with pagination
        const users = await User.find(filter)
            .select('-password -refreshTokens')
            .sort(sort)
            .skip(skip)
            .limit(parseInt(limit));
        const total = await User.countDocuments(filter);
        res.json({
            success: true,
            data: {
                users,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total,
                    pages: Math.ceil(total / parseInt(limit))
                }
            }
        });
    }
    catch (error) {
        logger.error('Error fetching users:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch users',
            code: 'FETCH_USERS_ERROR'
        });
    }
});
// GET /api/users/:id - Get user by ID
router.get('/:id', authenticate, requireOwnership('id'), async (req, res) => {
    try {
        const { id } = req.params;
        const user = await User.findById(id).select('-password -refreshTokens');
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found',
                code: 'USER_NOT_FOUND'
            });
        }
        res.json({
            success: true,
            data: { user }
        });
    }
    catch (error) {
        logger.error('Error fetching user:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch user',
            code: 'FETCH_USER_ERROR'
        });
    }
});
// POST /api/users - Create new user (Admin only)
router.post('/', authenticate, requireRole('admin'), userValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array(),
                code: 'VALIDATION_ERROR'
            });
        }
        const { email, name, roles, studentId, staffId, department, level, programme } = req.body;
        // Check if user already exists
        const existingUser = await User.findOne({ email });
        if (existingUser) {
            return res.status(409).json({
                success: false,
                message: 'User already exists',
                code: 'USER_EXISTS'
            });
        }
        // Create new user
        const user = new User({
            email,
            name,
            roles,
            studentId,
            staffId,
            department,
            level,
            programme,
            password: 'temp123', // Temporary password, user must change on first login
        });
        await user.save();
        // Publish real-time update
        await publishRealtimeUpdate('user_created', {
            user: {
                id: user._id,
                email: user.email,
                name: user.name,
                roles: user.roles,
                department: user.department
            },
            createdBy: req.user._id
        });
        // Log creation
        auditLogger('user_created', 'user', req.user._id, {
            targetUserId: user._id,
            email: user.email,
            roles: user.roles
        });
        res.status(201).json({
            success: true,
            message: 'User created successfully',
            data: {
                user: {
                    id: user._id,
                    email: user.email,
                    name: user.name,
                    roles: user.roles,
                    studentId: user.studentId,
                    staffId: user.staffId,
                    department: user.department,
                    level: user.level,
                    programme: user.programme,
                }
            }
        });
    }
    catch (error) {
        logger.error('Error creating user:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create user',
            code: 'CREATE_USER_ERROR'
        });
    }
});
// PUT /api/users/:id - Update user
router.put('/:id', authenticate, requireOwnership('id'), userValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array(),
                code: 'VALIDATION_ERROR'
            });
        }
        const { id } = req.params;
        const { name, roles, studentId, staffId, department, level, programme } = req.body;
        const user = await User.findById(id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found',
                code: 'USER_NOT_FOUND'
            });
        }
        // Update user fields
        user.name = name;
        if (req.user.roles.includes('admin')) {
            user.roles = roles;
        }
        user.studentId = studentId;
        user.staffId = staffId;
        user.department = department;
        user.level = level;
        user.programme = programme;
        user.updatedAt = new Date();
        await user.save();
        // Publish real-time update
        await publishRealtimeUpdate('user_updated', {
            user: {
                id: user._id,
                email: user.email,
                name: user.name,
                roles: user.roles,
                department: user.department
            },
            updatedBy: req.user._id
        });
        // Log update
        auditLogger('user_updated', 'user', req.user._id, {
            targetUserId: user._id,
            changes: { name, roles, department }
        });
        res.json({
            success: true,
            message: 'User updated successfully',
            data: {
                user: {
                    id: user._id,
                    email: user.email,
                    name: user.name,
                    roles: user.roles,
                    studentId: user.studentId,
                    staffId: user.staffId,
                    department: user.department,
                    level: user.level,
                    programme: user.programme,
                }
            }
        });
    }
    catch (error) {
        logger.error('Error updating user:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update user',
            code: 'UPDATE_USER_ERROR'
        });
    }
});
// DELETE /api/users/:id - Delete user (Admin only)
router.delete('/:id', authenticate, requireRole('admin'), async (req, res) => {
    try {
        const { id } = req.params;
        const user = await User.findById(id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found',
                code: 'USER_NOT_FOUND'
            });
        }
        // Soft delete - mark as inactive
        user.isActive = false;
        await user.save();
        // Publish real-time update
        await publishRealtimeUpdate('user_deleted', {
            userId: user._id,
            email: user.email,
            deletedBy: req.user._id
        });
        // Log deletion
        auditLogger('user_deleted', 'user', req.user._id, {
            targetUserId: user._id,
            email: user.email
        });
        res.json({
            success: true,
            message: 'User deleted successfully'
        });
    }
    catch (error) {
        logger.error('Error deleting user:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete user',
            code: 'DELETE_USER_ERROR'
        });
    }
});
// GET /api/users/profile - Get current user profile
router.get('/profile', authenticate, async (req, res) => {
    try {
        const user = await User.findById(req.user._id).select('-password -refreshTokens');
        res.json({
            success: true,
            data: { user }
        });
    }
    catch (error) {
        logger.error('Error fetching profile:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch profile',
            code: 'FETCH_PROFILE_ERROR'
        });
    }
});
// PUT /api/users/profile - Update current user profile
router.put('/profile', authenticate, async (req, res) => {
    try {
        const { name, department, level, programme } = req.body;
        const user = await User.findById(req.user._id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found',
                code: 'USER_NOT_FOUND'
            });
        }
        // Update profile fields
        if (name)
            user.name = name;
        if (department)
            user.department = department;
        if (level)
            user.level = level;
        if (programme)
            user.programme = programme;
        user.updatedAt = new Date();
        await user.save();
        // Publish real-time update
        await publishRealtimeUpdate('profile_updated', {
            userId: user._id,
            changes: { name, department, level, programme }
        });
        res.json({
            success: true,
            message: 'Profile updated successfully',
            data: {
                user: {
                    id: user._id,
                    email: user.email,
                    name: user.name,
                    roles: user.roles,
                    studentId: user.studentId,
                    staffId: user.staffId,
                    department: user.department,
                    level: user.level,
                    programme: user.programme,
                }
            }
        });
    }
    catch (error) {
        logger.error('Error updating profile:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update profile',
            code: 'UPDATE_PROFILE_ERROR'
        });
    }
});
export default router;
//# sourceMappingURL=users.js.map