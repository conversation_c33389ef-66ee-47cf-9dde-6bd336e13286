import express from 'express';
import { body, validationResult } from 'express-validator';
import { Department, User } from '../models/index.js';
import { authenticate, requireRole } from '../middleware/auth.js';
import { logger, auditLogger } from '../utils/logger.js';
import { publishRealtimeUpdate } from '../utils/realtime.js';
const router = express.Router();
// Validation rules
const departmentValidation = [
    body('name').trim().isLength({ min: 3 }).withMessage('Department name must be at least 3 characters'),
    body('code').trim().isLength({ min: 2, max: 10 }).withMessage('Department code must be 2-10 characters'),
    body('description').optional().trim(),
    body('head').optional().isMongoId().withMessage('Valid head of department ID required'),
    body('budget').optional().isFloat({ min: 0 }).withMessage('Budget must be a positive number'),
];
// GET /api/departments - List departments
router.get('/', authenticate, async (req, res) => {
    try {
        const { page = 1, limit = 20, search, sortBy = 'name', sortOrder = 'asc' } = req.query;
        // Build filter query
        const filter = { isActive: true };
        if (search) {
            filter.$or = [
                { name: { $regex: search, $options: 'i' } },
                { code: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } }
            ];
        }
        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
        // Execute query with pagination and population
        const departments = await Department.find(filter)
            .populate('head', 'name email staffId')
            .sort(sort)
            .skip(skip)
            .limit(parseInt(limit));
        const total = await Department.countDocuments(filter);
        res.json({
            success: true,
            data: {
                departments,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total,
                    pages: Math.ceil(total / parseInt(limit))
                }
            }
        });
    }
    catch (error) {
        logger.error('Error fetching departments:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch departments',
            code: 'FETCH_DEPARTMENTS_ERROR'
        });
    }
});
// GET /api/departments/:id - Get department by ID
router.get('/:id', authenticate, async (req, res) => {
    try {
        const { id } = req.params;
        const department = await Department.findById(id)
            .populate('head', 'name email staffId department');
        if (!department || !department.isActive) {
            return res.status(404).json({
                success: false,
                message: 'Department not found',
                code: 'DEPARTMENT_NOT_FOUND'
            });
        }
        // Get department statistics
        const Course = (await import('../models/index.js')).Course;
        const User = (await import('../models/index.js')).User;
        const stats = {
            courses: await Course.countDocuments({ department: department.name, isActive: true }),
            lecturers: await User.countDocuments({ department: department.name, roles: 'lecturer', isActive: true }),
            students: await User.countDocuments({ department: department.name, roles: 'student', isActive: true })
        };
        res.json({
            success: true,
            data: {
                department: {
                    ...department.toObject(),
                    stats
                }
            }
        });
    }
    catch (error) {
        logger.error('Error fetching department:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch department',
            code: 'FETCH_DEPARTMENT_ERROR'
        });
    }
});
// POST /api/departments - Create new department (Admin only)
router.post('/', authenticate, requireRole('admin'), departmentValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array(),
                code: 'VALIDATION_ERROR'
            });
        }
        const { name, code, description, head, budget, established } = req.body;
        // Check if department name or code already exists
        const existingDepartment = await Department.findOne({
            $or: [
                { name: { $regex: new RegExp(`^${name}$`, 'i') } },
                { code: { $regex: new RegExp(`^${code}$`, 'i') } }
            ],
            isActive: true
        });
        if (existingDepartment) {
            return res.status(409).json({
                success: false,
                message: 'Department with this name or code already exists',
                code: 'DEPARTMENT_EXISTS'
            });
        }
        // Verify head of department if provided
        if (head) {
            const headUser = await User.findOne({
                _id: head,
                roles: { $in: ['lecturer', 'admin'] },
                isActive: true
            });
            if (!headUser) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid head of department',
                    code: 'INVALID_HEAD'
                });
            }
        }
        // Create new department
        const department = new Department({
            name,
            code,
            description,
            head,
            budget: budget || 0,
            established: established ? new Date(established) : new Date()
        });
        await department.save();
        // Populate department for response
        await department.populate('head', 'name email staffId');
        // Publish real-time update
        await publishRealtimeUpdate('department_created', {
            department: {
                id: department._id,
                name: department.name,
                code: department.code,
                head: department.head?.name
            },
            createdBy: req.user._id
        });
        // Log creation
        auditLogger('department_created', 'department', req.user._id, {
            departmentId: department._id,
            name: department.name,
            code: department.code,
            head: department.head?.name
        });
        res.status(201).json({
            success: true,
            message: 'Department created successfully',
            data: { department }
        });
    }
    catch (error) {
        logger.error('Error creating department:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create department',
            code: 'CREATE_DEPARTMENT_ERROR'
        });
    }
});
// PUT /api/departments/:id - Update department (Admin only)
router.put('/:id', authenticate, requireRole('admin'), departmentValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array(),
                code: 'VALIDATION_ERROR'
            });
        }
        const { id } = req.params;
        const { name, code, description, head, budget } = req.body;
        const department = await Department.findById(id);
        if (!department || !department.isActive) {
            return res.status(404).json({
                success: false,
                message: 'Department not found',
                code: 'DEPARTMENT_NOT_FOUND'
            });
        }
        // Check if new name or code conflicts with existing departments
        if (name !== department.name || code !== department.code) {
            const existingDepartment = await Department.findOne({
                _id: { $ne: id },
                $or: [
                    { name: { $regex: new RegExp(`^${name}$`, 'i') } },
                    { code: { $regex: new RegExp(`^${code}$`, 'i') } }
                ],
                isActive: true
            });
            if (existingDepartment) {
                return res.status(409).json({
                    success: false,
                    message: 'Department with this name or code already exists',
                    code: 'DEPARTMENT_EXISTS'
                });
            }
        }
        // Verify head of department if provided
        if (head && head !== department.head?.toString()) {
            const headUser = await User.findOne({
                _id: head,
                roles: { $in: ['lecturer', 'admin'] },
                isActive: true
            });
            if (!headUser) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid head of department',
                    code: 'INVALID_HEAD'
                });
            }
        }
        // Update department fields
        department.name = name;
        department.code = code;
        department.description = description;
        department.head = head;
        department.budget = budget;
        department.updatedAt = new Date();
        await department.save();
        // Populate department for response
        await department.populate('head', 'name email staffId');
        // Publish real-time update
        await publishRealtimeUpdate('department_updated', {
            department: {
                id: department._id,
                name: department.name,
                code: department.code,
                head: department.head?.name
            },
            updatedBy: req.user._id
        });
        // Log update
        auditLogger('department_updated', 'department', req.user._id, {
            departmentId: department._id,
            name: department.name,
            code: department.code,
            changes: { name, code, head }
        });
        res.json({
            success: true,
            message: 'Department updated successfully',
            data: { department }
        });
    }
    catch (error) {
        logger.error('Error updating department:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update department',
            code: 'UPDATE_DEPARTMENT_ERROR'
        });
    }
});
// DELETE /api/departments/:id - Delete department (Admin only)
router.delete('/:id', authenticate, requireRole('admin'), async (req, res) => {
    try {
        const { id } = req.params;
        const department = await Department.findById(id);
        if (!department || !department.isActive) {
            return res.status(404).json({
                success: false,
                message: 'Department not found',
                code: 'DEPARTMENT_NOT_FOUND'
            });
        }
        // Check if department has active courses or users
        const Course = (await import('../models/index.js')).Course;
        const User = (await import('../models/index.js')).User;
        const activeCourses = await Course.countDocuments({
            department: department.name,
            isActive: true
        });
        const activeUsers = await User.countDocuments({
            department: department.name,
            isActive: true
        });
        if (activeCourses > 0 || activeUsers > 0) {
            return res.status(400).json({
                success: false,
                message: 'Cannot delete department with active courses or users',
                code: 'DEPARTMENT_HAS_ACTIVE_DATA',
                data: {
                    activeCourses,
                    activeUsers
                }
            });
        }
        // Soft delete - mark as inactive
        department.isActive = false;
        await department.save();
        // Publish real-time update
        await publishRealtimeUpdate('department_deleted', {
            departmentId: department._id,
            name: department.name,
            code: department.code,
            deletedBy: req.user._id
        });
        // Log deletion
        auditLogger('department_deleted', 'department', req.user._id, {
            departmentId: department._id,
            name: department.name,
            code: department.code
        });
        res.json({
            success: true,
            message: 'Department deleted successfully'
        });
    }
    catch (error) {
        logger.error('Error deleting department:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete department',
            code: 'DELETE_DEPARTMENT_ERROR'
        });
    }
});
// GET /api/departments/:id/stats - Get department statistics
router.get('/:id/stats', authenticate, async (req, res) => {
    try {
        const { id } = req.params;
        const department = await Department.findById(id);
        if (!department || !department.isActive) {
            return res.status(404).json({
                success: false,
                message: 'Department not found',
                code: 'DEPARTMENT_NOT_FOUND'
            });
        }
        const Course = (await import('../models/index.js')).Course;
        const User = (await import('../models/index.js')).User;
        const Enrollment = (await import('../models/index.js')).Enrollment;
        // Get comprehensive statistics
        const stats = {
            courses: await Course.countDocuments({ department: department.name, isActive: true }),
            lecturers: await User.countDocuments({ department: department.name, roles: 'lecturer', isActive: true }),
            students: await User.countDocuments({ department: department.name, roles: 'student', isActive: true }),
            totalEnrollments: await Enrollment.countDocuments({
                course: {
                    $in: await Course.find({ department: department.name }).select('_id')
                },
                status: 'enrolled'
            }),
            budget: department.budget,
            budgetUsed: 0, // This would be calculated based on actual expenses
            budgetRemaining: department.budget
        };
        res.json({
            success: true,
            data: { stats }
        });
    }
    catch (error) {
        logger.error('Error fetching department stats:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch department statistics',
            code: 'FETCH_DEPARTMENT_STATS_ERROR'
        });
    }
});
export default router;
//# sourceMappingURL=departments.js.map