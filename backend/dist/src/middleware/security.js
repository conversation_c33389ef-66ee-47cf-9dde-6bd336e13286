import helmet from 'helmet';
import xss from 'xss-clean';
import hpp from 'hpp';
import mongoSanitize from 'mongo-sanitize';
import { logger } from '../utils/logger.js';
// Security headers middleware
export const securityHeaders = helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
        },
    },
    crossOriginEmbedderPolicy: false,
    hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
    }
});
// XSS protection middleware
export const xssProtection = xss();
// HTTP Parameter Pollution protection
export const hppProtection = hpp();
// MongoDB injection protection
export const mongoInjectionProtection = (req, res, next) => {
    // Sanitize request body
    if (req.body) {
        req.body = mongoSanitize(req.body);
    }
    // Sanitize request query
    if (req.query) {
        req.query = mongoSanitize(req.query);
    }
    // Sanitize request params
    if (req.params) {
        req.params = mongoSanitize(req.params);
    }
    next();
};
// Rate limiting for sensitive endpoints
export const sensitiveEndpointRateLimit = (req, res, next) => {
    // This would integrate with express-rate-limit
    // For now, just pass through
    next();
};
// File upload security
export const fileUploadSecurity = (req, res, next) => {
    // Check file types and sizes
    if (req.files) {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
        const maxSize = 5 * 1024 * 1024; // 5MB
        for (const file of req.files) {
            if (!allowedTypes.includes(file.mimetype)) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid file type',
                    code: 'INVALID_FILE_TYPE'
                });
            }
            if (file.size > maxSize) {
                return res.status(400).json({
                    success: false,
                    message: 'File too large',
                    code: 'FILE_TOO_LARGE'
                });
            }
        }
    }
    next();
};
// Security logging middleware
export const securityLogging = (req, res, next) => {
    // Log suspicious activities
    const suspiciousPatterns = [
        /script/i,
        /javascript/i,
        /onload/i,
        /onerror/i,
        /eval/i,
        /expression/i,
        /\$where/i,
        /\$regex/i
    ];
    const checkSuspicious = (obj) => {
        if (typeof obj === 'string') {
            return suspiciousPatterns.some(pattern => pattern.test(obj));
        }
        if (typeof obj === 'object' && obj !== null) {
            return Object.values(obj).some(checkSuspicious);
        }
        return false;
    };
    if (checkSuspicious(req.body) || checkSuspicious(req.query) || checkSuspicious(req.params)) {
        logger.warn('Suspicious activity detected:', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            url: req.url,
            method: req.method,
            body: req.body,
            query: req.query,
            params: req.params
        });
    }
    next();
};
// CORS configuration
export const corsConfig = {
    origin: function (origin, callback) {
        // Allow requests with no origin (mobile apps, etc.)
        if (!origin)
            return callback(null, true);
        const allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:3001',
            'https://university-portal.com',
            'https://www.university-portal.com'
        ];
        if (allowedOrigins.includes(origin)) {
            callback(null, true);
        }
        else {
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    optionsSuccessStatus: 200
};
export default {
    securityHeaders,
    xssProtection,
    hppProtection,
    mongoInjectionProtection,
    sensitiveEndpointRateLimit,
    fileUploadSecurity,
    securityLogging,
    corsConfig
};
//# sourceMappingURL=security.js.map