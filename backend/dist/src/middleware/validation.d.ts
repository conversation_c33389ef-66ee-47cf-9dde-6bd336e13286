export declare const validateRequest: (req: any, res: any, next: any) => any;
export declare const validateQuery: (req: any, res: any, next: any) => any;
export declare const validateParams: (req: any, res: any, next: any) => any;
declare const _default: {
    validateRequest: (req: any, res: any, next: any) => any;
    validateQuery: (req: any, res: any, next: any) => any;
    validateParams: (req: any, res: any, next: any) => any;
};
export default _default;
//# sourceMappingURL=validation.d.ts.map