export declare const securityHeaders: (req: import("http").IncomingMessage, res: import("http").ServerResponse, next: (err?: unknown) => void) => void;
export declare const xssProtection: any;
export declare const hppProtection: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
export declare const mongoInjectionProtection: (req: any, res: any, next: any) => void;
export declare const sensitiveEndpointRateLimit: (req: any, res: any, next: any) => void;
export declare const fileUploadSecurity: (req: any, res: any, next: any) => any;
export declare const securityLogging: (req: any, res: any, next: any) => void;
export declare const corsConfig: {
    origin: (origin: any, callback: any) => any;
    credentials: boolean;
    optionsSuccessStatus: number;
};
declare const _default: {
    securityHeaders: (req: import("http").IncomingMessage, res: import("http").ServerResponse, next: (err?: unknown) => void) => void;
    xssProtection: any;
    hppProtection: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
    mongoInjectionProtection: (req: any, res: any, next: any) => void;
    sensitiveEndpointRateLimit: (req: any, res: any, next: any) => void;
    fileUploadSecurity: (req: any, res: any, next: any) => any;
    securityLogging: (req: any, res: any, next: any) => void;
    corsConfig: {
        origin: (origin: any, callback: any) => any;
        credentials: boolean;
        optionsSuccessStatus: number;
    };
};
export default _default;
//# sourceMappingURL=security.d.ts.map