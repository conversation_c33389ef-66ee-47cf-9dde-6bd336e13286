{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../src/middleware/auth.ts"], "names": [], "mappings": "AAAA,OAAO,GAAG,MAAM,cAAc,CAAC;AAC/B,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AACjC,OAAO,MAAM,MAAM,oBAAoB,CAAC;AACxC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAE5C,MAAM,WAAW,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAE1C,uBAAuB;AACvB,MAAM,OAAO,YAAY;IACvB,MAAM,CAAC,mBAAmB,CAAC,OAAO;QAChC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE;YAC1C,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,eAAe;YACrC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM;YACzB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,OAAO;QACjC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE;YACjD,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,gBAAgB;YACtC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM;YACzB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK;QAClC,IAAI,CAAC;YACH,OAAO,MAAM,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE;gBACjD,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM;gBACzB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ;aAC9B,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK;QACnC,IAAI,CAAC;YACH,OAAO,MAAM,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE;gBACxD,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM;gBACzB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ;aAC9B,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG;QAC7B,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,aAAa;QAClC,MAAM,WAAW,CAAC,KAAK,CAAC,aAAa,GAAG,EAAE,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,GAAG;QACjC,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,GAAG,CAAC,aAAa,GAAG,EAAE,CAAC,CAAC;QACzD,OAAO,MAAM,KAAK,MAAM,CAAC;IAC3B,CAAC;CACF;AAED,4BAA4B;AAC5B,MAAM,CAAC,MAAM,YAAY,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;IACnD,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAE7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE,eAAe;aACtB,CAAC,CAAC;QACL,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAEtC,gCAAgC;QAChC,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAC5D,MAAM,aAAa,GAAG,MAAM,YAAY,CAAC,kBAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEzE,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE,eAAe;aACtB,CAAC,CAAC;QACL,CAAC;QAED,kCAAkC;QAClC,IAAI,IAAI,GAAG,MAAM,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;YAC9E,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,4BAA4B;oBACrC,IAAI,EAAE,gBAAgB;iBACvB,CAAC,CAAC;YACL,CAAC;YACD,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;QAED,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC;QAClB,GAAG,CAAC,YAAY,GAAG,OAAO,CAAC;QAC3B,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC7C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,eAAe;YACxB,IAAI,EAAE,eAAe;SACtB,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAEF,uCAAuC;AACvC,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,GAAG,YAAY,EAAE,EAAE;IAC7C,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;QACxB,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE,eAAe;aACtB,CAAC,CAAC;QACL,CAAC;QAED,MAAM,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;QACjC,MAAM,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAEpE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,0BAA0B;gBACnC,IAAI,EAAE,mBAAmB;aAC1B,CAAC,CAAC;QACL,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,6CAA6C;AAC7C,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,UAAU,EAAE,EAAE;IAC1C,OAAO,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;QAC9B,IAAI,CAAC;YACH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,yBAAyB;oBAClC,IAAI,EAAE,eAAe;iBACtB,CAAC,CAAC;YACL,CAAC;YAED,oBAAoB;YACpB,MAAM,QAAQ,GAAG,eAAe,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YAC/C,IAAI,eAAe,GAAG,MAAM,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAEtD,IAAI,eAAe,EAAE,CAAC;gBACpB,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YAChD,CAAC;iBAAM,CAAC;gBACN,gCAAgC;gBAChC,eAAe,GAAG,MAAM,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACzE,MAAM,WAAW,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC;YACnG,CAAC;YAED,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC1C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,0BAA0B;oBACnC,IAAI,EAAE,yBAAyB;iBAChC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE,wBAAwB;aAC/B,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,8BAA8B;AAC9B,MAAM,CAAC,MAAM,UAAU,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;IACjD,IAAI,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE,eAAe;aACtB,CAAC,CAAC;QACL,CAAC;QAED,iDAAiD;QACjD,MAAM,gBAAgB,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;QACvE,MAAM,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAEjF,IAAI,WAAW,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE,oBAAoB;aAC3B,CAAC,CAAC;QACL,CAAC;QAED,0CAA0C;QAC1C,IAAI,WAAW,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACvC,MAAM,WAAW,GAAG,GAAG,CAAC,YAAY,EAAE,WAAW,CAAC;YAClD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,2BAA2B;oBACpC,IAAI,EAAE,2BAA2B;iBAClC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;QACxC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,kBAAkB;YAC3B,IAAI,EAAE,iBAAiB;SACxB,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAEF,gCAAgC;AAChC,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,aAAa,GAAG,QAAQ,EAAE,EAAE;IAC3D,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;QACxB,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE,eAAe;aACtB,CAAC,CAAC;QACL,CAAC;QAED,sCAAsC;QACtC,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,MAAM,cAAc,GAAG,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE5E,IAAI,cAAc,IAAI,cAAc,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACtD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,gCAAgC;gBACzC,IAAI,EAAE,wBAAwB;aAC/B,CAAC,CAAC;QACL,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,mBAAmB;AACnB,KAAK,UAAU,gBAAgB,CAAC,MAAM;IACpC,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,QAAQ,MAAM,EAAE,CAAC;QAClC,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACnD,OAAO,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACpD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACtD,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED,KAAK,UAAU,SAAS,CAAC,IAAI;IAC3B,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC;QACpC,MAAM,WAAW,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;IAClF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;IAC7C,CAAC;AACH,CAAC;AAED,KAAK,UAAU,kBAAkB,CAAC,MAAM,EAAE,SAAS;IACjD,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC;YACxC,GAAG,EAAE;gBACH,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE;gBAC7B,EAAE,QAAQ,EAAE,IAAI,EAAE;aACnB;SACF,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAElB,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACvD,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAED,2BAA2B;AAC3B,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,EAAE;IACrE,OAAO,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;QAC9B,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;YAC9B,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE5C,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;gBAClB,MAAM,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC;YAC5D,CAAC;YAED,IAAI,OAAO,GAAG,WAAW,EAAE,CAAC;gBAC1B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,mBAAmB;oBAC5B,IAAI,EAAE,qBAAqB;oBAC3B,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;iBACvC,CAAC,CAAC;YACL,CAAC;YAED,GAAG,CAAC,GAAG,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;YAC1C,GAAG,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC;YAErE,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC5C,IAAI,EAAE,CAAC,CAAC,+CAA+C;QACzD,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,sBAAsB;AACtB,MAAM,CAAC,MAAM,cAAc,GAAG,eAAe,CAC3C,MAAM,CAAC,SAAS,CAAC,QAAQ,EACzB,CAAC,EAAE,8BAA8B;AACjC,CAAC,GAAG,EAAE,EAAE,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,CAC7C,CAAC;AAEF,oBAAoB;AACpB,MAAM,CAAC,MAAM,YAAY,GAAG,eAAe,CACzC,MAAM,CAAC,SAAS,CAAC,QAAQ,EACzB,MAAM,CAAC,SAAS,CAAC,WAAW,EAC5B,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,GAAG,CAAC,EAAE,EAAE,CACzB,CAAC;AAEF,0BAA0B;AAC1B,MAAM,CAAC,MAAM,eAAe,GAAG,eAAe,CAC5C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAC3B,CAAC,GAAG,EAAE,EAAE,CAAC,UAAU,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,CACnC,CAAC"}