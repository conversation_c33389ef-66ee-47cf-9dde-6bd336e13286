import jwt from 'jsonwebtoken';
export declare class TokenManager {
    static generateAccessToken(payload: any): never;
    static generateRefreshToken(payload: any): never;
    static verifyAccessToken(token: any): Promise<jwt.SigningKeyCallback>;
    static verifyRefreshToken(token: any): Promise<jwt.SigningKeyCallback>;
    static blacklistToken(jti: any): Promise<void>;
    static isTokenBlacklisted(jti: any): Promise<boolean>;
}
export declare const authenticate: (req: any, res: any, next: any) => Promise<any>;
export declare const requireRole: (...allowedRoles: any[]) => (req: any, res: any, next: any) => any;
export declare const hasPermission: (permission: any) => (req: any, res: any, next: any) => Promise<any>;
export declare const requireMFA: (req: any, res: any, next: any) => Promise<any>;
export declare const requireOwnership: (resourceField?: string) => (req: any, res: any, next: any) => any;
export declare const createRateLimit: (windowMs: any, maxRequests: any, keyGenerator: any) => (req: any, res: any, next: any) => Promise<any>;
export declare const loginRateLimit: (req: any, res: any, next: any) => Promise<any>;
export declare const apiRateLimit: (req: any, res: any, next: any) => Promise<any>;
export declare const socketRateLimit: (req: any, res: any, next: any) => Promise<any>;
//# sourceMappingURL=auth.d.ts.map