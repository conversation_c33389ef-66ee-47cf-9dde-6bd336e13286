import mongoose from 'mongoose';
export declare const Course: mongoose.Model<{
    isActive: boolean;
    code: string;
    semester: string;
    lecturer: mongoose.Types.ObjectId;
    department: string;
    academicYear: string;
    title: string;
    credits: number;
    schedule: mongoose.Types.DocumentArray<{
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }> & {
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }>;
    enrolled: number;
    capacity: number;
    prerequisites: mongoose.Types.ObjectId[];
    objectives: string[];
    learningOutcomes: string[];
    assessmentMethods: string[];
    textbooks: mongoose.Types.DocumentArray<{
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }> & {
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }>;
    resources: string[];
    description?: string | null | undefined;
    startDate?: NativeDate | null | undefined;
    endDate?: NativeDate | null | undefined;
    syllabus?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    isActive: boolean;
    code: string;
    semester: string;
    lecturer: mongoose.Types.ObjectId;
    department: string;
    academicYear: string;
    title: string;
    credits: number;
    schedule: mongoose.Types.DocumentArray<{
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }> & {
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }>;
    enrolled: number;
    capacity: number;
    prerequisites: mongoose.Types.ObjectId[];
    objectives: string[];
    learningOutcomes: string[];
    assessmentMethods: string[];
    textbooks: mongoose.Types.DocumentArray<{
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }> & {
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }>;
    resources: string[];
    description?: string | null | undefined;
    startDate?: NativeDate | null | undefined;
    endDate?: NativeDate | null | undefined;
    syllabus?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}> & {
    isActive: boolean;
    code: string;
    semester: string;
    lecturer: mongoose.Types.ObjectId;
    department: string;
    academicYear: string;
    title: string;
    credits: number;
    schedule: mongoose.Types.DocumentArray<{
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }> & {
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }>;
    enrolled: number;
    capacity: number;
    prerequisites: mongoose.Types.ObjectId[];
    objectives: string[];
    learningOutcomes: string[];
    assessmentMethods: string[];
    textbooks: mongoose.Types.DocumentArray<{
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }> & {
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }>;
    resources: string[];
    description?: string | null | undefined;
    startDate?: NativeDate | null | undefined;
    endDate?: NativeDate | null | undefined;
    syllabus?: string | null | undefined;
} & mongoose.DefaultTimestampProps & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}, {
    isActive: boolean;
    code: string;
    semester: string;
    lecturer: mongoose.Types.ObjectId;
    department: string;
    academicYear: string;
    title: string;
    credits: number;
    schedule: mongoose.Types.DocumentArray<{
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }> & {
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }>;
    enrolled: number;
    capacity: number;
    prerequisites: mongoose.Types.ObjectId[];
    objectives: string[];
    learningOutcomes: string[];
    assessmentMethods: string[];
    textbooks: mongoose.Types.DocumentArray<{
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }> & {
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }>;
    resources: string[];
    description?: string | null | undefined;
    startDate?: NativeDate | null | undefined;
    endDate?: NativeDate | null | undefined;
    syllabus?: string | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    isActive: boolean;
    code: string;
    semester: string;
    lecturer: mongoose.Types.ObjectId;
    department: string;
    academicYear: string;
    title: string;
    credits: number;
    schedule: mongoose.Types.DocumentArray<{
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }> & {
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }>;
    enrolled: number;
    capacity: number;
    prerequisites: mongoose.Types.ObjectId[];
    objectives: string[];
    learningOutcomes: string[];
    assessmentMethods: string[];
    textbooks: mongoose.Types.DocumentArray<{
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }> & {
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }>;
    resources: string[];
    description?: string | null | undefined;
    startDate?: NativeDate | null | undefined;
    endDate?: NativeDate | null | undefined;
    syllabus?: string | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}>> & mongoose.FlatRecord<{
    isActive: boolean;
    code: string;
    semester: string;
    lecturer: mongoose.Types.ObjectId;
    department: string;
    academicYear: string;
    title: string;
    credits: number;
    schedule: mongoose.Types.DocumentArray<{
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }> & {
        type?: "lecture" | "lab" | "tutorial" | "seminar" | null | undefined;
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }>;
    enrolled: number;
    capacity: number;
    prerequisites: mongoose.Types.ObjectId[];
    objectives: string[];
    learningOutcomes: string[];
    assessmentMethods: string[];
    textbooks: mongoose.Types.DocumentArray<{
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }> & {
        required?: boolean | null | undefined;
        title?: string | null | undefined;
        author?: string | null | undefined;
        edition?: string | null | undefined;
        isbn?: string | null | undefined;
    }>;
    resources: string[];
    description?: string | null | undefined;
    startDate?: NativeDate | null | undefined;
    endDate?: NativeDate | null | undefined;
    syllabus?: string | null | undefined;
} & mongoose.DefaultTimestampProps> & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}>>;
export default Course;
//# sourceMappingURL=Course.d.ts.map