{"version": 3, "file": "Assignment.js", "sourceRoot": "", "sources": ["../../../src/models/Assignment.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,UAAU,CAAC;AAEhC,MAAM,gBAAgB,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC;IAC3C,KAAK,EAAE;QACL,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,IAAI;KACX;IACD,WAAW,EAAE;QACX,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,IAAI;KACX;IACD,MAAM,EAAE;QACN,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,GAAG,EAAE,QAAQ;QACb,QAAQ,EAAE,IAAI;KACf;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,GAAG,EAAE,MAAM;QACX,QAAQ,EAAE,IAAI;KACf;IACD,OAAO,EAAE;QACP,IAAI,EAAE,IAAI;QACV,QAAQ,EAAE,IAAI;KACf;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;QACd,GAAG,EAAE,CAAC;KACP;IACD,IAAI,EAAE;QACJ,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,eAAe,CAAC;QAChE,QAAQ,EAAE,IAAI;KACf;IACD,YAAY,EAAE;QACZ,IAAI,EAAE,MAAM;KACb;IACD,WAAW,EAAE,CAAC;YACZ,QAAQ,EAAE,MAAM;YAChB,YAAY,EAAE,MAAM;YACpB,QAAQ,EAAE,MAAM;YAChB,IAAI,EAAE,MAAM;YACZ,GAAG,EAAE,MAAM;SACZ,CAAC;IACF,WAAW,EAAE;QACX,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,IAAI;KACd;IACD,WAAW,EAAE,CAAC;YACZ,OAAO,EAAE;gBACP,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;gBACpC,GAAG,EAAE,MAAM;aACZ;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,IAAI;gBACV,OAAO,EAAE,IAAI,CAAC,GAAG;aAClB;YACD,KAAK,EAAE,CAAC;oBACN,QAAQ,EAAE,MAAM;oBAChB,YAAY,EAAE,MAAM;oBACpB,QAAQ,EAAE,MAAM;oBAChB,IAAI,EAAE,MAAM;oBACZ,GAAG,EAAE,MAAM;iBACZ,CAAC;YACF,QAAQ,EAAE,MAAM;YAChB,KAAK,EAAE,MAAM;YACb,QAAQ,EAAE,MAAM;YAChB,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE;gBACR,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;gBACpC,GAAG,EAAE,MAAM;aACZ;SACF,CAAC;IACF,qBAAqB,EAAE;QACrB,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,IAAI;KACd;IACD,qBAAqB,EAAE;QACrB,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,EAAE;QACX,GAAG,EAAE,CAAC;QACN,GAAG,EAAE,GAAG;KACT;IACD,iBAAiB,EAAE;QACjB,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,KAAK;KACf;IACD,cAAc,EAAE;QACd,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC;KACP;IACD,MAAM,EAAE,CAAC;YACP,SAAS,EAAE,MAAM;YACjB,WAAW,EAAE,MAAM;YACnB,SAAS,EAAE,MAAM;YACjB,MAAM,EAAE,MAAM;SACf,CAAC;IACF,IAAI,EAAE,CAAC,MAAM,CAAC;IACd,iBAAiB,EAAE;QACjB,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,KAAK;KACf;IACD,YAAY,EAAE;QACZ,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC;KACP;CACF,EAAE;IACD,UAAU,EAAE,IAAI;IAChB,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;IAC1B,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;CAC7B,CAAC,CAAC;AAEH,UAAU;AACV,gBAAgB,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;AACtC,gBAAgB,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AACxC,gBAAgB,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;AACvC,gBAAgB,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AACpC,gBAAgB,CAAC,KAAK,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3C,gBAAgB,CAAC,KAAK,CAAC,EAAE,qBAAqB,EAAE,CAAC,EAAE,CAAC,CAAC;AAErD,+BAA+B;AAC/B,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC;IAC9C,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;AACjC,CAAC,CAAC,CAAC;AAEH,6BAA6B;AAC7B,gBAAgB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC;IAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IACtC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,MAAM,CAAC;IAE9E,IAAI,KAAK,KAAK,CAAC;QAAE,OAAO,gBAAgB,CAAC;IACzC,IAAI,MAAM,KAAK,KAAK;QAAE,OAAO,YAAY,CAAC;IAC1C,IAAI,MAAM,KAAK,CAAC;QAAE,OAAO,YAAY,CAAC;IACtC,OAAO,GAAG,MAAM,IAAI,KAAK,SAAS,CAAC;AACrC,CAAC,CAAC,CAAC;AAEH,6BAA6B;AAC7B,gBAAgB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC;IAC5C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACnC,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC;IAE3C,IAAI,IAAI,IAAI,CAAC;QAAE,OAAO,SAAS,CAAC;IAEhC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACtD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAE5E,IAAI,IAAI,GAAG,CAAC;QAAE,OAAO,GAAG,IAAI,UAAU,KAAK,QAAQ,CAAC;IACpD,OAAO,GAAG,KAAK,QAAQ,CAAC;AAC1B,CAAC,CAAC,CAAC;AAEH,wCAAwC;AACxC,gBAAgB,CAAC,GAAG,CAAC,MAAM,EAAE,UAAS,IAAI;IACxC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;QAC9C,wDAAwD;QACxD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IACD,IAAI,EAAE,CAAC;AACT,CAAC,CAAC,CAAC;AAEH,8BAA8B;AAC9B,gBAAgB,CAAC,OAAO,CAAC,gBAAgB,GAAG,UAAS,SAAS,EAAE,KAAK,EAAE,QAAQ;IAC7E,6BAA6B;IAC7B,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,CAAC;IAE9F,IAAI,kBAAkB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAClD,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;IAClD,CAAC;IAED,yBAAyB;IACzB,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,CAAC;IAChG,IAAI,kBAAkB,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;QACrD,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACjD,CAAC;IAED,yBAAyB;IACzB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,MAAM,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;IAElC,MAAM,UAAU,GAAG;QACjB,OAAO,EAAE,SAAS;QAClB,WAAW,EAAE,GAAG;QAChB,KAAK,EAAE,KAAK,IAAI,EAAE;QAClB,QAAQ,EAAE,QAAQ,IAAI,EAAE;QACxB,MAAM,EAAE,MAAM;KACf,CAAC;IAEF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAClC,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;AACrB,CAAC,CAAC;AAEF,6BAA6B;AAC7B,gBAAgB,CAAC,OAAO,CAAC,eAAe,GAAG,UAAS,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;IACtF,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,CAAC;IAEtF,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC1C,CAAC;IAED,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;IACzB,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC/B,UAAU,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;IACjC,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAE/B,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;AACrB,CAAC,CAAC;AAEF,mCAAmC;AACnC,gBAAgB,CAAC,OAAO,CAAC,oBAAoB,GAAG,UAAS,SAAS;IAChE,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,CAAC;AAC5E,CAAC,CAAC;AAEF,kCAAkC;AAClC,gBAAgB,CAAC,OAAO,CAAC,YAAY,GAAG,UAAS,QAAQ;IACvD,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;AAC5E,CAAC,CAAC;AAEF,oCAAoC;AACpC,gBAAgB,CAAC,OAAO,CAAC,cAAc,GAAG,UAAS,UAAU;IAC3D,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AAC9E,CAAC,CAAC;AAEF,6CAA6C;AAC7C,gBAAgB,CAAC,OAAO,CAAC,YAAY,GAAG,UAAS,IAAI,GAAG,CAAC;IACvD,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;IAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;IAEhD,OAAO,IAAI,CAAC,IAAI,CAAC;QACf,OAAO,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,EAAE;QAC/C,WAAW,EAAE,IAAI;KAClB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AACtC,CAAC,CAAC;AAEF,4CAA4C;AAC5C,gBAAgB,CAAC,OAAO,CAAC,WAAW,GAAG;IACrC,OAAO,IAAI,CAAC,IAAI,CAAC;QACf,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,IAAI,EAAE,EAAE;QAC5B,WAAW,EAAE,IAAI;KAClB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AACtC,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;AACzE,eAAe,UAAU,CAAC"}