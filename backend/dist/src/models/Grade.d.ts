import mongoose from 'mongoose';
export declare const Grade: mongoose.Model<{
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    semester: string;
    student: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    percentage: number;
    academicYear: string;
    grade: "D" | "A" | "B" | "C" | "F";
    score: number;
    maxScore: number;
    gradedBy: mongoose.Types.ObjectId;
    gradedAt: NativeDate;
    isFinal: boolean;
    curveApplied: boolean;
    curveValue: number;
    latePenalty: number;
    extraCredit: number;
    assignment?: mongoose.Types.ObjectId | null | undefined;
    comments?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    semester: string;
    student: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    percentage: number;
    academicYear: string;
    grade: "D" | "A" | "B" | "C" | "F";
    score: number;
    maxScore: number;
    gradedBy: mongoose.Types.ObjectId;
    gradedAt: NativeDate;
    isFinal: boolean;
    curveApplied: boolean;
    curveValue: number;
    latePenalty: number;
    extraCredit: number;
    assignment?: mongoose.Types.ObjectId | null | undefined;
    comments?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}> & {
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    semester: string;
    student: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    percentage: number;
    academicYear: string;
    grade: "D" | "A" | "B" | "C" | "F";
    score: number;
    maxScore: number;
    gradedBy: mongoose.Types.ObjectId;
    gradedAt: NativeDate;
    isFinal: boolean;
    curveApplied: boolean;
    curveValue: number;
    latePenalty: number;
    extraCredit: number;
    assignment?: mongoose.Types.ObjectId | null | undefined;
    comments?: string | null | undefined;
} & mongoose.DefaultTimestampProps & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}, {
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    semester: string;
    student: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    percentage: number;
    academicYear: string;
    grade: "D" | "A" | "B" | "C" | "F";
    score: number;
    maxScore: number;
    gradedBy: mongoose.Types.ObjectId;
    gradedAt: NativeDate;
    isFinal: boolean;
    curveApplied: boolean;
    curveValue: number;
    latePenalty: number;
    extraCredit: number;
    assignment?: mongoose.Types.ObjectId | null | undefined;
    comments?: string | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    semester: string;
    student: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    percentage: number;
    academicYear: string;
    grade: "D" | "A" | "B" | "C" | "F";
    score: number;
    maxScore: number;
    gradedBy: mongoose.Types.ObjectId;
    gradedAt: NativeDate;
    isFinal: boolean;
    curveApplied: boolean;
    curveValue: number;
    latePenalty: number;
    extraCredit: number;
    assignment?: mongoose.Types.ObjectId | null | undefined;
    comments?: string | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}>> & mongoose.FlatRecord<{
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    semester: string;
    student: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    percentage: number;
    academicYear: string;
    grade: "D" | "A" | "B" | "C" | "F";
    score: number;
    maxScore: number;
    gradedBy: mongoose.Types.ObjectId;
    gradedAt: NativeDate;
    isFinal: boolean;
    curveApplied: boolean;
    curveValue: number;
    latePenalty: number;
    extraCredit: number;
    assignment?: mongoose.Types.ObjectId | null | undefined;
    comments?: string | null | undefined;
} & mongoose.DefaultTimestampProps> & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}>>;
export default Grade;
//# sourceMappingURL=Grade.d.ts.map