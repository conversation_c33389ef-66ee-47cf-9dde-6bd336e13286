import mongoose from 'mongoose';
const enrollmentSchema = new mongoose.Schema({
    student: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    course: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course',
        required: true
    },
    semester: {
        type: String,
        required: true
    },
    academicYear: {
        type: String,
        required: true
    },
    status: {
        type: String,
        enum: ['enrolled', 'dropped', 'completed', 'failed'],
        default: 'enrolled'
    },
    enrolledAt: {
        type: Date,
        default: Date.now
    },
    droppedAt: {
        type: Date
    },
    completedAt: {
        type: Date
    },
    finalGrade: {
        type: String,
        enum: ['A', 'B', 'C', 'D', 'F', 'P', 'NP']
    },
    creditsEarned: {
        type: Number,
        default: 0
    },
    notes: {
        type: String
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Indexes
enrollmentSchema.index({ student: 1, course: 1, semester: 1, academicYear: 1 }, { unique: true });
enrollmentSchema.index({ student: 1 });
enrollmentSchema.index({ course: 1 });
enrollmentSchema.index({ status: 1 });
enrollmentSchema.index({ semester: 1, academicYear: 1 });
// Virtual for duration
enrollmentSchema.virtual('duration').get(function () {
    if (this.droppedAt) {
        return this.droppedAt - this.enrolledAt;
    }
    return Date.now() - this.enrolledAt;
});
// Pre-save middleware to set droppedAt
enrollmentSchema.pre('save', function (next) {
    if (this.isModified('status') && this.status === 'dropped' && !this.droppedAt) {
        this.droppedAt = new Date();
    }
    if (this.isModified('status') && this.status === 'completed' && !this.completedAt) {
        this.completedAt = new Date();
    }
    next();
});
// Method to drop enrollment
enrollmentSchema.methods.drop = function () {
    this.status = 'dropped';
    this.droppedAt = new Date();
    return this.save();
};
// Method to complete enrollment
enrollmentSchema.methods.complete = function (finalGrade) {
    this.status = 'completed';
    this.finalGrade = finalGrade;
    this.completedAt = new Date();
    return this.save();
};
// Static method to find by student
enrollmentSchema.statics.findByStudent = function (studentId) {
    return this.find({ student: studentId }).populate('course');
};
// Static method to find by course
enrollmentSchema.statics.findByCourse = function (courseId) {
    return this.find({ course: courseId }).populate('student');
};
// Static method to find active enrollments
enrollmentSchema.statics.findActive = function () {
    return this.find({ status: 'enrolled' });
};
export const Enrollment = mongoose.model('Enrollment', enrollmentSchema);
export default Enrollment;
//# sourceMappingURL=Enrollment.js.map