import mongoose from 'mongoose';
export declare const User: mongoose.Model<{
    password: string;
    name: string;
    email: string;
    isActive: boolean;
    roles: ("lecturer" | "student" | "admin" | "finance" | "registrar")[];
    loginAttempts: number;
    mfaEnabled: boolean;
    refreshTokens: mongoose.Types.DocumentArray<{
        createdAt: NativeDate;
        token?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }> & {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }>;
    isLocked: boolean;
    level?: string | null | undefined;
    studentId?: string | null | undefined;
    staffId?: string | null | undefined;
    department?: string | null | undefined;
    programme?: string | null | undefined;
    lastLogin?: NativeDate | null | undefined;
    lockUntil?: NativeDate | null | undefined;
    mfaSecret?: string | null | undefined;
    phone?: string | null | undefined;
    profilePicture?: string | null | undefined;
    address?: {
        street?: string | null | undefined;
        city?: string | null | undefined;
        state?: string | null | undefined;
        zipCode?: string | null | undefined;
        country?: string | null | undefined;
    } | null | undefined;
    emergencyContact?: {
        name?: string | null | undefined;
        email?: string | null | undefined;
        phone?: string | null | undefined;
        relationship?: string | null | undefined;
    } | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    password: string;
    name: string;
    email: string;
    isActive: boolean;
    roles: ("lecturer" | "student" | "admin" | "finance" | "registrar")[];
    loginAttempts: number;
    mfaEnabled: boolean;
    refreshTokens: mongoose.Types.DocumentArray<{
        createdAt: NativeDate;
        token?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }> & {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }>;
    isLocked: boolean;
    level?: string | null | undefined;
    studentId?: string | null | undefined;
    staffId?: string | null | undefined;
    department?: string | null | undefined;
    programme?: string | null | undefined;
    lastLogin?: NativeDate | null | undefined;
    lockUntil?: NativeDate | null | undefined;
    mfaSecret?: string | null | undefined;
    phone?: string | null | undefined;
    profilePicture?: string | null | undefined;
    address?: {
        street?: string | null | undefined;
        city?: string | null | undefined;
        state?: string | null | undefined;
        zipCode?: string | null | undefined;
        country?: string | null | undefined;
    } | null | undefined;
    emergencyContact?: {
        name?: string | null | undefined;
        email?: string | null | undefined;
        phone?: string | null | undefined;
        relationship?: string | null | undefined;
    } | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}> & {
    password: string;
    name: string;
    email: string;
    isActive: boolean;
    roles: ("lecturer" | "student" | "admin" | "finance" | "registrar")[];
    loginAttempts: number;
    mfaEnabled: boolean;
    refreshTokens: mongoose.Types.DocumentArray<{
        createdAt: NativeDate;
        token?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }> & {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }>;
    isLocked: boolean;
    level?: string | null | undefined;
    studentId?: string | null | undefined;
    staffId?: string | null | undefined;
    department?: string | null | undefined;
    programme?: string | null | undefined;
    lastLogin?: NativeDate | null | undefined;
    lockUntil?: NativeDate | null | undefined;
    mfaSecret?: string | null | undefined;
    phone?: string | null | undefined;
    profilePicture?: string | null | undefined;
    address?: {
        street?: string | null | undefined;
        city?: string | null | undefined;
        state?: string | null | undefined;
        zipCode?: string | null | undefined;
        country?: string | null | undefined;
    } | null | undefined;
    emergencyContact?: {
        name?: string | null | undefined;
        email?: string | null | undefined;
        phone?: string | null | undefined;
        relationship?: string | null | undefined;
    } | null | undefined;
} & mongoose.DefaultTimestampProps & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}, {
    password: string;
    name: string;
    email: string;
    isActive: boolean;
    roles: ("lecturer" | "student" | "admin" | "finance" | "registrar")[];
    loginAttempts: number;
    mfaEnabled: boolean;
    refreshTokens: mongoose.Types.DocumentArray<{
        createdAt: NativeDate;
        token?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }> & {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }>;
    isLocked: boolean;
    level?: string | null | undefined;
    studentId?: string | null | undefined;
    staffId?: string | null | undefined;
    department?: string | null | undefined;
    programme?: string | null | undefined;
    lastLogin?: NativeDate | null | undefined;
    lockUntil?: NativeDate | null | undefined;
    mfaSecret?: string | null | undefined;
    phone?: string | null | undefined;
    profilePicture?: string | null | undefined;
    address?: {
        street?: string | null | undefined;
        city?: string | null | undefined;
        state?: string | null | undefined;
        zipCode?: string | null | undefined;
        country?: string | null | undefined;
    } | null | undefined;
    emergencyContact?: {
        name?: string | null | undefined;
        email?: string | null | undefined;
        phone?: string | null | undefined;
        relationship?: string | null | undefined;
    } | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    password: string;
    name: string;
    email: string;
    isActive: boolean;
    roles: ("lecturer" | "student" | "admin" | "finance" | "registrar")[];
    loginAttempts: number;
    mfaEnabled: boolean;
    refreshTokens: mongoose.Types.DocumentArray<{
        createdAt: NativeDate;
        token?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }> & {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }>;
    isLocked: boolean;
    level?: string | null | undefined;
    studentId?: string | null | undefined;
    staffId?: string | null | undefined;
    department?: string | null | undefined;
    programme?: string | null | undefined;
    lastLogin?: NativeDate | null | undefined;
    lockUntil?: NativeDate | null | undefined;
    mfaSecret?: string | null | undefined;
    phone?: string | null | undefined;
    profilePicture?: string | null | undefined;
    address?: {
        street?: string | null | undefined;
        city?: string | null | undefined;
        state?: string | null | undefined;
        zipCode?: string | null | undefined;
        country?: string | null | undefined;
    } | null | undefined;
    emergencyContact?: {
        name?: string | null | undefined;
        email?: string | null | undefined;
        phone?: string | null | undefined;
        relationship?: string | null | undefined;
    } | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}>> & mongoose.FlatRecord<{
    password: string;
    name: string;
    email: string;
    isActive: boolean;
    roles: ("lecturer" | "student" | "admin" | "finance" | "registrar")[];
    loginAttempts: number;
    mfaEnabled: boolean;
    refreshTokens: mongoose.Types.DocumentArray<{
        createdAt: NativeDate;
        token?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }> & {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }>;
    isLocked: boolean;
    level?: string | null | undefined;
    studentId?: string | null | undefined;
    staffId?: string | null | undefined;
    department?: string | null | undefined;
    programme?: string | null | undefined;
    lastLogin?: NativeDate | null | undefined;
    lockUntil?: NativeDate | null | undefined;
    mfaSecret?: string | null | undefined;
    phone?: string | null | undefined;
    profilePicture?: string | null | undefined;
    address?: {
        street?: string | null | undefined;
        city?: string | null | undefined;
        state?: string | null | undefined;
        zipCode?: string | null | undefined;
        country?: string | null | undefined;
    } | null | undefined;
    emergencyContact?: {
        name?: string | null | undefined;
        email?: string | null | undefined;
        phone?: string | null | undefined;
        relationship?: string | null | undefined;
    } | null | undefined;
} & mongoose.DefaultTimestampProps> & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}>>;
export default User;
//# sourceMappingURL=User.d.ts.map