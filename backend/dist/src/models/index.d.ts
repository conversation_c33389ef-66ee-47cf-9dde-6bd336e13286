import mongoose from 'mongoose';
declare const User: mongoose.Model<{
    password: string;
    name: string;
    email: string;
    isActive: boolean;
    roles: ("lecturer" | "student" | "admin" | "finance" | "registrar")[];
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    loginAttempts: number;
    mfaEnabled: boolean;
    refreshTokens: mongoose.Types.DocumentArray<{
        createdAt: NativeDate;
        token?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }> & {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }>;
    permissions: string[];
    level?: number | null | undefined;
    studentId?: string | null | undefined;
    staffId?: string | null | undefined;
    department?: string | null | undefined;
    avatar?: string | null | undefined;
    programme?: string | null | undefined;
    lastLogin?: NativeDate | null | undefined;
    lockUntil?: NativeDate | null | undefined;
    mfaSecret?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    password: string;
    name: string;
    email: string;
    isActive: boolean;
    roles: ("lecturer" | "student" | "admin" | "finance" | "registrar")[];
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    loginAttempts: number;
    mfaEnabled: boolean;
    refreshTokens: mongoose.Types.DocumentArray<{
        createdAt: NativeDate;
        token?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }> & {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }>;
    permissions: string[];
    level?: number | null | undefined;
    studentId?: string | null | undefined;
    staffId?: string | null | undefined;
    department?: string | null | undefined;
    avatar?: string | null | undefined;
    programme?: string | null | undefined;
    lastLogin?: NativeDate | null | undefined;
    lockUntil?: NativeDate | null | undefined;
    mfaSecret?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}> & {
    password: string;
    name: string;
    email: string;
    isActive: boolean;
    roles: ("lecturer" | "student" | "admin" | "finance" | "registrar")[];
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    loginAttempts: number;
    mfaEnabled: boolean;
    refreshTokens: mongoose.Types.DocumentArray<{
        createdAt: NativeDate;
        token?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }> & {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }>;
    permissions: string[];
    level?: number | null | undefined;
    studentId?: string | null | undefined;
    staffId?: string | null | undefined;
    department?: string | null | undefined;
    avatar?: string | null | undefined;
    programme?: string | null | undefined;
    lastLogin?: NativeDate | null | undefined;
    lockUntil?: NativeDate | null | undefined;
    mfaSecret?: string | null | undefined;
} & mongoose.DefaultTimestampProps & Required<{
    _id: string;
}> & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}, {
    password: string;
    name: string;
    email: string;
    isActive: boolean;
    roles: ("lecturer" | "student" | "admin" | "finance" | "registrar")[];
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    loginAttempts: number;
    mfaEnabled: boolean;
    refreshTokens: mongoose.Types.DocumentArray<{
        createdAt: NativeDate;
        token?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }> & {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }>;
    permissions: string[];
    level?: number | null | undefined;
    studentId?: string | null | undefined;
    staffId?: string | null | undefined;
    department?: string | null | undefined;
    avatar?: string | null | undefined;
    programme?: string | null | undefined;
    lastLogin?: NativeDate | null | undefined;
    lockUntil?: NativeDate | null | undefined;
    mfaSecret?: string | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    password: string;
    name: string;
    email: string;
    isActive: boolean;
    roles: ("lecturer" | "student" | "admin" | "finance" | "registrar")[];
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    loginAttempts: number;
    mfaEnabled: boolean;
    refreshTokens: mongoose.Types.DocumentArray<{
        createdAt: NativeDate;
        token?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }> & {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }>;
    permissions: string[];
    level?: number | null | undefined;
    studentId?: string | null | undefined;
    staffId?: string | null | undefined;
    department?: string | null | undefined;
    avatar?: string | null | undefined;
    programme?: string | null | undefined;
    lastLogin?: NativeDate | null | undefined;
    lockUntil?: NativeDate | null | undefined;
    mfaSecret?: string | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}>> & mongoose.FlatRecord<{
    password: string;
    name: string;
    email: string;
    isActive: boolean;
    roles: ("lecturer" | "student" | "admin" | "finance" | "registrar")[];
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    loginAttempts: number;
    mfaEnabled: boolean;
    refreshTokens: mongoose.Types.DocumentArray<{
        createdAt: NativeDate;
        token?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }> & {
        createdAt: NativeDate;
        token?: string | null | undefined;
    }>;
    permissions: string[];
    level?: number | null | undefined;
    studentId?: string | null | undefined;
    staffId?: string | null | undefined;
    department?: string | null | undefined;
    avatar?: string | null | undefined;
    programme?: string | null | undefined;
    lastLogin?: NativeDate | null | undefined;
    lockUntil?: NativeDate | null | undefined;
    mfaSecret?: string | null | undefined;
} & mongoose.DefaultTimestampProps> & Required<{
    _id: string;
}> & {
    __v: number;
}>>;
declare const Course: mongoose.Model<{
    isActive: boolean;
    code: string;
    semester: string;
    lecturer: string;
    department: string;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    academicYear: string;
    title: string;
    credits: number;
    schedule: mongoose.Types.DocumentArray<{
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }> & {
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }>;
    enrolled: number;
    capacity: number;
    prerequisites: string[];
    description?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    isActive: boolean;
    code: string;
    semester: string;
    lecturer: string;
    department: string;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    academicYear: string;
    title: string;
    credits: number;
    schedule: mongoose.Types.DocumentArray<{
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }> & {
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }>;
    enrolled: number;
    capacity: number;
    prerequisites: string[];
    description?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
}> & {
    isActive: boolean;
    code: string;
    semester: string;
    lecturer: string;
    department: string;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    academicYear: string;
    title: string;
    credits: number;
    schedule: mongoose.Types.DocumentArray<{
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }> & {
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }>;
    enrolled: number;
    capacity: number;
    prerequisites: string[];
    description?: string | null | undefined;
} & mongoose.DefaultTimestampProps & Required<{
    _id: string;
}> & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
}, {
    isActive: boolean;
    code: string;
    semester: string;
    lecturer: string;
    department: string;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    academicYear: string;
    title: string;
    credits: number;
    schedule: mongoose.Types.DocumentArray<{
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }> & {
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }>;
    enrolled: number;
    capacity: number;
    prerequisites: string[];
    description?: string | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    isActive: boolean;
    code: string;
    semester: string;
    lecturer: string;
    department: string;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    academicYear: string;
    title: string;
    credits: number;
    schedule: mongoose.Types.DocumentArray<{
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }> & {
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }>;
    enrolled: number;
    capacity: number;
    prerequisites: string[];
    description?: string | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
}>> & mongoose.FlatRecord<{
    isActive: boolean;
    code: string;
    semester: string;
    lecturer: string;
    department: string;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    academicYear: string;
    title: string;
    credits: number;
    schedule: mongoose.Types.DocumentArray<{
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }> & {
        startTime?: string | null | undefined;
        day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday" | null | undefined;
        endTime?: string | null | undefined;
        room?: string | null | undefined;
    }>;
    enrolled: number;
    capacity: number;
    prerequisites: string[];
    description?: string | null | undefined;
} & mongoose.DefaultTimestampProps> & Required<{
    _id: string;
}> & {
    __v: number;
}>>;
declare const Enrollment: mongoose.Model<{
    semester: string;
    student: string;
    course: string;
    _id: string;
    status: "completed" | "failed" | "enrolled" | "dropped";
    academicYear: string;
    enrolledAt: NativeDate;
    finalGrade?: string | null | undefined;
    creditsEarned?: number | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    semester: string;
    student: string;
    course: string;
    _id: string;
    status: "completed" | "failed" | "enrolled" | "dropped";
    academicYear: string;
    enrolledAt: NativeDate;
    finalGrade?: string | null | undefined;
    creditsEarned?: number | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
}> & {
    semester: string;
    student: string;
    course: string;
    _id: string;
    status: "completed" | "failed" | "enrolled" | "dropped";
    academicYear: string;
    enrolledAt: NativeDate;
    finalGrade?: string | null | undefined;
    creditsEarned?: number | null | undefined;
} & mongoose.DefaultTimestampProps & Required<{
    _id: string;
}> & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
}, {
    semester: string;
    student: string;
    course: string;
    _id: string;
    status: "completed" | "failed" | "enrolled" | "dropped";
    academicYear: string;
    enrolledAt: NativeDate;
    finalGrade?: string | null | undefined;
    creditsEarned?: number | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    semester: string;
    student: string;
    course: string;
    _id: string;
    status: "completed" | "failed" | "enrolled" | "dropped";
    academicYear: string;
    enrolledAt: NativeDate;
    finalGrade?: string | null | undefined;
    creditsEarned?: number | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
}>> & mongoose.FlatRecord<{
    semester: string;
    student: string;
    course: string;
    _id: string;
    status: "completed" | "failed" | "enrolled" | "dropped";
    academicYear: string;
    enrolledAt: NativeDate;
    finalGrade?: string | null | undefined;
    creditsEarned?: number | null | undefined;
} & mongoose.DefaultTimestampProps> & Required<{
    _id: string;
}> & {
    __v: number;
}>>;
declare const Grade: mongoose.Model<{
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    semester: string;
    student: string;
    course: string;
    _id: string;
    academicYear: string;
    isFinal: boolean;
    percentage?: number | null | undefined;
    assignment?: string | null | undefined;
    grade?: string | null | undefined;
    score?: number | null | undefined;
    maxScore?: number | null | undefined;
    gradedBy?: string | null | undefined;
    gradedAt?: NativeDate | null | undefined;
    comments?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    semester: string;
    student: string;
    course: string;
    _id: string;
    academicYear: string;
    isFinal: boolean;
    percentage?: number | null | undefined;
    assignment?: string | null | undefined;
    grade?: string | null | undefined;
    score?: number | null | undefined;
    maxScore?: number | null | undefined;
    gradedBy?: string | null | undefined;
    gradedAt?: NativeDate | null | undefined;
    comments?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
}> & {
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    semester: string;
    student: string;
    course: string;
    _id: string;
    academicYear: string;
    isFinal: boolean;
    percentage?: number | null | undefined;
    assignment?: string | null | undefined;
    grade?: string | null | undefined;
    score?: number | null | undefined;
    maxScore?: number | null | undefined;
    gradedBy?: string | null | undefined;
    gradedAt?: NativeDate | null | undefined;
    comments?: string | null | undefined;
} & mongoose.DefaultTimestampProps & Required<{
    _id: string;
}> & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
}, {
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    semester: string;
    student: string;
    course: string;
    _id: string;
    academicYear: string;
    isFinal: boolean;
    percentage?: number | null | undefined;
    assignment?: string | null | undefined;
    grade?: string | null | undefined;
    score?: number | null | undefined;
    maxScore?: number | null | undefined;
    gradedBy?: string | null | undefined;
    gradedAt?: NativeDate | null | undefined;
    comments?: string | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    semester: string;
    student: string;
    course: string;
    _id: string;
    academicYear: string;
    isFinal: boolean;
    percentage?: number | null | undefined;
    assignment?: string | null | undefined;
    grade?: string | null | undefined;
    score?: number | null | undefined;
    maxScore?: number | null | undefined;
    gradedBy?: string | null | undefined;
    gradedAt?: NativeDate | null | undefined;
    comments?: string | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
}>> & mongoose.FlatRecord<{
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    semester: string;
    student: string;
    course: string;
    _id: string;
    academicYear: string;
    isFinal: boolean;
    percentage?: number | null | undefined;
    assignment?: string | null | undefined;
    grade?: string | null | undefined;
    score?: number | null | undefined;
    maxScore?: number | null | undefined;
    gradedBy?: string | null | undefined;
    gradedAt?: NativeDate | null | undefined;
    comments?: string | null | undefined;
} & mongoose.DefaultTimestampProps> & Required<{
    _id: string;
}> & {
    __v: number;
}>>;
declare const Assignment: mongoose.Model<{
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    lecturer: string;
    course: string;
    dueDate: NativeDate;
    isPublished: boolean;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    title: string;
    maxScore: number;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }>;
    submissions: mongoose.Types.DocumentArray<{
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }> & {
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }>;
    description?: string | null | undefined;
    instructions?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    lecturer: string;
    course: string;
    dueDate: NativeDate;
    isPublished: boolean;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    title: string;
    maxScore: number;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }>;
    submissions: mongoose.Types.DocumentArray<{
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }> & {
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }>;
    description?: string | null | undefined;
    instructions?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
}> & {
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    lecturer: string;
    course: string;
    dueDate: NativeDate;
    isPublished: boolean;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    title: string;
    maxScore: number;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }>;
    submissions: mongoose.Types.DocumentArray<{
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }> & {
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }>;
    description?: string | null | undefined;
    instructions?: string | null | undefined;
} & mongoose.DefaultTimestampProps & Required<{
    _id: string;
}> & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
}, {
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    lecturer: string;
    course: string;
    dueDate: NativeDate;
    isPublished: boolean;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    title: string;
    maxScore: number;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }>;
    submissions: mongoose.Types.DocumentArray<{
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }> & {
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }>;
    description?: string | null | undefined;
    instructions?: string | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    lecturer: string;
    course: string;
    dueDate: NativeDate;
    isPublished: boolean;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    title: string;
    maxScore: number;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }>;
    submissions: mongoose.Types.DocumentArray<{
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }> & {
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }>;
    description?: string | null | undefined;
    instructions?: string | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
}>> & mongoose.FlatRecord<{
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    lecturer: string;
    course: string;
    dueDate: NativeDate;
    isPublished: boolean;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    title: string;
    maxScore: number;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        mimeType?: string | null | undefined;
    }>;
    submissions: mongoose.Types.DocumentArray<{
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }> & {
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            mimeType?: string | null | undefined;
        }>;
        student?: string | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: string | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        submittedAt?: NativeDate | null | undefined;
        feedback?: string | null | undefined;
    }>;
    description?: string | null | undefined;
    instructions?: string | null | undefined;
} & mongoose.DefaultTimestampProps> & Required<{
    _id: string;
}> & {
    __v: number;
}>>;
declare const Attendance: mongoose.Model<{
    session: string;
    student: string;
    course: string;
    date: NativeDate;
    _id: string;
    status: "present" | "absent" | "late" | "excused";
    markedAt: NativeDate;
    notes?: string | null | undefined;
    markedBy?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    session: string;
    student: string;
    course: string;
    date: NativeDate;
    _id: string;
    status: "present" | "absent" | "late" | "excused";
    markedAt: NativeDate;
    notes?: string | null | undefined;
    markedBy?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
}> & {
    session: string;
    student: string;
    course: string;
    date: NativeDate;
    _id: string;
    status: "present" | "absent" | "late" | "excused";
    markedAt: NativeDate;
    notes?: string | null | undefined;
    markedBy?: string | null | undefined;
} & mongoose.DefaultTimestampProps & Required<{
    _id: string;
}> & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
}, {
    session: string;
    student: string;
    course: string;
    date: NativeDate;
    _id: string;
    status: "present" | "absent" | "late" | "excused";
    markedAt: NativeDate;
    notes?: string | null | undefined;
    markedBy?: string | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    session: string;
    student: string;
    course: string;
    date: NativeDate;
    _id: string;
    status: "present" | "absent" | "late" | "excused";
    markedAt: NativeDate;
    notes?: string | null | undefined;
    markedBy?: string | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
}>> & mongoose.FlatRecord<{
    session: string;
    student: string;
    course: string;
    date: NativeDate;
    _id: string;
    status: "present" | "absent" | "late" | "excused";
    markedAt: NativeDate;
    notes?: string | null | undefined;
    markedBy?: string | null | undefined;
} & mongoose.DefaultTimestampProps> & Required<{
    _id: string;
}> & {
    __v: number;
}>>;
declare const Session: mongoose.Model<{
    type: "lecture" | "lab" | "tutorial" | "seminar";
    lecturer: string;
    course: string;
    date: NativeDate;
    startTime: string;
    _id: string;
    endTime: string;
    attendanceTaken: boolean;
    notes?: string | null | undefined;
    room?: string | null | undefined;
    topic?: string | null | undefined;
    qrCode?: string | null | undefined;
    qrCodeExpiry?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    type: "lecture" | "lab" | "tutorial" | "seminar";
    lecturer: string;
    course: string;
    date: NativeDate;
    startTime: string;
    _id: string;
    endTime: string;
    attendanceTaken: boolean;
    notes?: string | null | undefined;
    room?: string | null | undefined;
    topic?: string | null | undefined;
    qrCode?: string | null | undefined;
    qrCodeExpiry?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
}> & {
    type: "lecture" | "lab" | "tutorial" | "seminar";
    lecturer: string;
    course: string;
    date: NativeDate;
    startTime: string;
    _id: string;
    endTime: string;
    attendanceTaken: boolean;
    notes?: string | null | undefined;
    room?: string | null | undefined;
    topic?: string | null | undefined;
    qrCode?: string | null | undefined;
    qrCodeExpiry?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps & Required<{
    _id: string;
}> & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
}, {
    type: "lecture" | "lab" | "tutorial" | "seminar";
    lecturer: string;
    course: string;
    date: NativeDate;
    startTime: string;
    _id: string;
    endTime: string;
    attendanceTaken: boolean;
    notes?: string | null | undefined;
    room?: string | null | undefined;
    topic?: string | null | undefined;
    qrCode?: string | null | undefined;
    qrCodeExpiry?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    type: "lecture" | "lab" | "tutorial" | "seminar";
    lecturer: string;
    course: string;
    date: NativeDate;
    startTime: string;
    _id: string;
    endTime: string;
    attendanceTaken: boolean;
    notes?: string | null | undefined;
    room?: string | null | undefined;
    topic?: string | null | undefined;
    qrCode?: string | null | undefined;
    qrCodeExpiry?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
}>> & mongoose.FlatRecord<{
    type: "lecture" | "lab" | "tutorial" | "seminar";
    lecturer: string;
    course: string;
    date: NativeDate;
    startTime: string;
    _id: string;
    endTime: string;
    attendanceTaken: boolean;
    notes?: string | null | undefined;
    room?: string | null | undefined;
    topic?: string | null | undefined;
    qrCode?: string | null | undefined;
    qrCodeExpiry?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps> & Required<{
    _id: string;
}> & {
    __v: number;
}>>;
declare const Department: mongoose.Model<{
    name: string;
    isActive: boolean;
    code: string;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    budget: number;
    description?: string | null | undefined;
    head?: string | null | undefined;
    established?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    name: string;
    isActive: boolean;
    code: string;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    budget: number;
    description?: string | null | undefined;
    head?: string | null | undefined;
    established?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
}> & {
    name: string;
    isActive: boolean;
    code: string;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    budget: number;
    description?: string | null | undefined;
    head?: string | null | undefined;
    established?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps & Required<{
    _id: string;
}> & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
}, {
    name: string;
    isActive: boolean;
    code: string;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    budget: number;
    description?: string | null | undefined;
    head?: string | null | undefined;
    established?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    name: string;
    isActive: boolean;
    code: string;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    budget: number;
    description?: string | null | undefined;
    head?: string | null | undefined;
    established?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
}>> & mongoose.FlatRecord<{
    name: string;
    isActive: boolean;
    code: string;
    createdAt: NativeDate;
    _id: string;
    updatedAt: NativeDate;
    budget: number;
    description?: string | null | undefined;
    head?: string | null | undefined;
    established?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps> & Required<{
    _id: string;
}> & {
    __v: number;
}>>;
declare const Notification: mongoose.Model<{
    message: string;
    type: "info" | "error" | "success" | "warning";
    user: string;
    read: boolean;
    createdAt: NativeDate;
    _id: string;
    title: string;
    link?: string | null | undefined;
    metadata?: any;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    message: string;
    type: "info" | "error" | "success" | "warning";
    user: string;
    read: boolean;
    createdAt: NativeDate;
    _id: string;
    title: string;
    link?: string | null | undefined;
    metadata?: any;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
}> & {
    message: string;
    type: "info" | "error" | "success" | "warning";
    user: string;
    read: boolean;
    createdAt: NativeDate;
    _id: string;
    title: string;
    link?: string | null | undefined;
    metadata?: any;
} & mongoose.DefaultTimestampProps & Required<{
    _id: string;
}> & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
}, {
    message: string;
    type: "info" | "error" | "success" | "warning";
    user: string;
    read: boolean;
    createdAt: NativeDate;
    _id: string;
    title: string;
    link?: string | null | undefined;
    metadata?: any;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    message: string;
    type: "info" | "error" | "success" | "warning";
    user: string;
    read: boolean;
    createdAt: NativeDate;
    _id: string;
    title: string;
    link?: string | null | undefined;
    metadata?: any;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
}>> & mongoose.FlatRecord<{
    message: string;
    type: "info" | "error" | "success" | "warning";
    user: string;
    read: boolean;
    createdAt: NativeDate;
    _id: string;
    title: string;
    link?: string | null | undefined;
    metadata?: any;
} & mongoose.DefaultTimestampProps> & Required<{
    _id: string;
}> & {
    __v: number;
}>>;
declare const Permission: mongoose.Model<{
    name: string;
    isActive: boolean;
    roles: string[];
    resource: string;
    _id: string;
    action: string;
    description?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    name: string;
    isActive: boolean;
    roles: string[];
    resource: string;
    _id: string;
    action: string;
    description?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
}> & {
    name: string;
    isActive: boolean;
    roles: string[];
    resource: string;
    _id: string;
    action: string;
    description?: string | null | undefined;
} & mongoose.DefaultTimestampProps & Required<{
    _id: string;
}> & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
}, {
    name: string;
    isActive: boolean;
    roles: string[];
    resource: string;
    _id: string;
    action: string;
    description?: string | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    name: string;
    isActive: boolean;
    roles: string[];
    resource: string;
    _id: string;
    action: string;
    description?: string | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
}>> & mongoose.FlatRecord<{
    name: string;
    isActive: boolean;
    roles: string[];
    resource: string;
    _id: string;
    action: string;
    description?: string | null | undefined;
} & mongoose.DefaultTimestampProps> & Required<{
    _id: string;
}> & {
    __v: number;
}>>;
import { PaymentMethod, PaymentTransaction, PaymentWebhook, PaymentReconciliation, Fee } from './Payment.js';
export { User, Course, Enrollment, Grade, Assignment, Attendance, Session, Department, Notification, Permission, PaymentMethod, PaymentTransaction, PaymentWebhook, PaymentReconciliation, Fee, };
//# sourceMappingURL=index.d.ts.map