{"version": 3, "file": "Session.js", "sourceRoot": "", "sources": ["../../../src/models/Session.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,UAAU,CAAC;AAEhC,MAAM,aAAa,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC;IACxC,MAAM,EAAE;QACN,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,GAAG,EAAE,QAAQ;QACb,QAAQ,EAAE,IAAI;KACf;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,GAAG,EAAE,MAAM;QACX,QAAQ,EAAE,IAAI;KACf;IACD,IAAI,EAAE;QACJ,IAAI,EAAE,IAAI;QACV,QAAQ,EAAE,IAAI;KACf;IACD,SAAS,EAAE;QACT,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;KACf;IACD,OAAO,EAAE;QACP,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;KACf;IACD,IAAI,EAAE;QACJ,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,IAAI;KACX;IACD,IAAI,EAAE;QACJ,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,CAAC;QAC/C,QAAQ,EAAE,IAAI;KACf;IACD,KAAK,EAAE;QACL,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,IAAI;KACX;IACD,WAAW,EAAE;QACX,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,IAAI;KACX;IACD,UAAU,EAAE,CAAC,MAAM,CAAC;IACpB,SAAS,EAAE,CAAC;YACV,KAAK,EAAE,MAAM;YACb,IAAI,EAAE,MAAM;YACZ,GAAG,EAAE,MAAM;YACX,WAAW,EAAE,MAAM;SACpB,CAAC;IACF,eAAe,EAAE;QACf,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,KAAK;KACf;IACD,iBAAiB,EAAE;QACjB,IAAI,EAAE,IAAI;KACX;IACD,iBAAiB,EAAE;QACjB,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,GAAG,EAAE,MAAM;KACZ;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,IAAI;KACd;IACD,KAAK,EAAE;QACL,IAAI,EAAE,MAAM;KACb;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,MAAM;KACb;IACD,WAAW,EAAE;QACX,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,GAAG,EAAE,SAAS;KACf;IACD,eAAe,EAAE;QACf,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,GAAG,EAAE,SAAS;KACf;CACF,EAAE;IACD,UAAU,EAAE,IAAI;IAChB,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;IAC1B,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;CAC7B,CAAC,CAAC;AAEH,UAAU;AACV,aAAa,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;AACnC,aAAa,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AACrC,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AACjC,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AACjC,aAAa,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AACrC,aAAa,CAAC,KAAK,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE,CAAC,CAAC;AAE5C,uBAAuB;AACvB,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC;IACpC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;IACvD,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IACnD,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;IAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,sBAAsB;AAC/D,CAAC,CAAC,CAAC;AAEH,qBAAqB;AACrB,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC;IAClC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;IAC9F,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IAE1F,IAAI,GAAG,GAAG,YAAY;QAAE,OAAO,UAAU,CAAC;IAC1C,IAAI,GAAG,IAAI,YAAY,IAAI,GAAG,IAAI,UAAU;QAAE,OAAO,SAAS,CAAC;IAC/D,IAAI,GAAG,GAAG,UAAU;QAAE,OAAO,WAAW,CAAC;IAEzC,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC,CAAC;AAEH,+BAA+B;AAC/B,aAAa,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC;IAC3C,yCAAyC;IACzC,OAAO,CAAC,CAAC;AACX,CAAC,CAAC,CAAC;AAEH,wCAAwC;AACxC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,UAAS,IAAI;IACrC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QACnC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACvD,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QAEnD,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IACD,IAAI,EAAE,CAAC;AACT,CAAC,CAAC,CAAC;AAEH,4BAA4B;AAC5B,aAAa,CAAC,OAAO,CAAC,cAAc,GAAG,UAAS,OAAO;IACrD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAC5B,IAAI,CAAC,iBAAiB,GAAG,IAAI,IAAI,EAAE,CAAC;IACpC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC;IACjC,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;AACrB,CAAC,CAAC;AAEF,2BAA2B;AAC3B,aAAa,CAAC,OAAO,CAAC,MAAM,GAAG;IAC7B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IACtB,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;AACrB,CAAC,CAAC;AAEF,+BAA+B;AAC/B,aAAa,CAAC,OAAO,CAAC,UAAU,GAAG,UAAS,OAAO,EAAE,YAAY,EAAE,UAAU;IAC3E,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC;IAC9B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;IAC1B,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IAC7B,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;IACnC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;IACnC,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;AACrB,CAAC,CAAC;AAEF,kCAAkC;AAClC,aAAa,CAAC,OAAO,CAAC,YAAY,GAAG,UAAS,QAAQ;IACpD,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3E,CAAC,CAAC;AAEF,oCAAoC;AACpC,aAAa,CAAC,OAAO,CAAC,cAAc,GAAG,UAAS,UAAU;IACxD,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/E,CAAC,CAAC;AAEF,sCAAsC;AACtC,aAAa,CAAC,OAAO,CAAC,eAAe,GAAG,UAAS,SAAS,EAAE,OAAO;IACjE,OAAO,IAAI,CAAC,IAAI,CAAC;QACf,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE;QACxC,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AACnD,CAAC,CAAC;AAEF,0CAA0C;AAC1C,aAAa,CAAC,OAAO,CAAC,YAAY,GAAG,UAAS,IAAI,GAAG,CAAC;IACpD,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;IAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;IAEhD,OAAO,IAAI,CAAC,IAAI,CAAC;QACf,IAAI,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,EAAE;QAC5C,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AACnD,CAAC,CAAC;AAEF,yCAAyC;AACzC,aAAa,CAAC,OAAO,CAAC,kBAAkB,GAAG;IACzC,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;IACzB,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;IACjC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;IAEzC,OAAO,IAAI,CAAC,IAAI,CAAC;QACf,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE;QACpC,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;AACxD,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;AAChE,eAAe,OAAO,CAAC"}