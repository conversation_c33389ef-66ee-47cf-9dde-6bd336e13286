import mongoose from 'mongoose';
export declare const Assignment: mongoose.Model<{
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    lecturer: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    dueDate: NativeDate;
    isPublished: boolean;
    title: string;
    maxScore: number;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }>;
    submissions: mongoose.Types.DocumentArray<{
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }> & {
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }>;
    lateSubmissionAllowed: boolean;
    latePenaltyPercentage: number;
    allowResubmission: boolean;
    maxSubmissions: number;
    rubric: mongoose.Types.DocumentArray<{
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }> & {
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }>;
    tags: string[];
    isGroupAssignment: boolean;
    maxGroupSize: number;
    description?: string | null | undefined;
    instructions?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    lecturer: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    dueDate: NativeDate;
    isPublished: boolean;
    title: string;
    maxScore: number;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }>;
    submissions: mongoose.Types.DocumentArray<{
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }> & {
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }>;
    lateSubmissionAllowed: boolean;
    latePenaltyPercentage: number;
    allowResubmission: boolean;
    maxSubmissions: number;
    rubric: mongoose.Types.DocumentArray<{
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }> & {
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }>;
    tags: string[];
    isGroupAssignment: boolean;
    maxGroupSize: number;
    description?: string | null | undefined;
    instructions?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}> & {
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    lecturer: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    dueDate: NativeDate;
    isPublished: boolean;
    title: string;
    maxScore: number;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }>;
    submissions: mongoose.Types.DocumentArray<{
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }> & {
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }>;
    lateSubmissionAllowed: boolean;
    latePenaltyPercentage: number;
    allowResubmission: boolean;
    maxSubmissions: number;
    rubric: mongoose.Types.DocumentArray<{
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }> & {
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }>;
    tags: string[];
    isGroupAssignment: boolean;
    maxGroupSize: number;
    description?: string | null | undefined;
    instructions?: string | null | undefined;
} & mongoose.DefaultTimestampProps & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}, {
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    lecturer: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    dueDate: NativeDate;
    isPublished: boolean;
    title: string;
    maxScore: number;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }>;
    submissions: mongoose.Types.DocumentArray<{
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }> & {
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }>;
    lateSubmissionAllowed: boolean;
    latePenaltyPercentage: number;
    allowResubmission: boolean;
    maxSubmissions: number;
    rubric: mongoose.Types.DocumentArray<{
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }> & {
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }>;
    tags: string[];
    isGroupAssignment: boolean;
    maxGroupSize: number;
    description?: string | null | undefined;
    instructions?: string | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    lecturer: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    dueDate: NativeDate;
    isPublished: boolean;
    title: string;
    maxScore: number;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }>;
    submissions: mongoose.Types.DocumentArray<{
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }> & {
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }>;
    lateSubmissionAllowed: boolean;
    latePenaltyPercentage: number;
    allowResubmission: boolean;
    maxSubmissions: number;
    rubric: mongoose.Types.DocumentArray<{
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }> & {
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }>;
    tags: string[];
    isGroupAssignment: boolean;
    maxGroupSize: number;
    description?: string | null | undefined;
    instructions?: string | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}>> & mongoose.FlatRecord<{
    type: "assignment" | "quiz" | "exam" | "project" | "participation";
    lecturer: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    dueDate: NativeDate;
    isPublished: boolean;
    title: string;
    maxScore: number;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }>;
    submissions: mongoose.Types.DocumentArray<{
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }> & {
        submittedAt: NativeDate;
        files: mongoose.Types.DocumentArray<{
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }> & {
            filename?: string | null | undefined;
            size?: number | null | undefined;
            url?: string | null | undefined;
            originalName?: string | null | undefined;
            mimetype?: string | null | undefined;
        }>;
        student?: mongoose.Types.ObjectId | null | undefined;
        grade?: number | null | undefined;
        gradedBy?: mongoose.Types.ObjectId | null | undefined;
        gradedAt?: NativeDate | null | undefined;
        comments?: string | null | undefined;
        feedback?: string | null | undefined;
    }>;
    lateSubmissionAllowed: boolean;
    latePenaltyPercentage: number;
    allowResubmission: boolean;
    maxSubmissions: number;
    rubric: mongoose.Types.DocumentArray<{
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }> & {
        description?: string | null | undefined;
        criterion?: string | null | undefined;
        maxPoints?: number | null | undefined;
        weight?: number | null | undefined;
    }>;
    tags: string[];
    isGroupAssignment: boolean;
    maxGroupSize: number;
    description?: string | null | undefined;
    instructions?: string | null | undefined;
} & mongoose.DefaultTimestampProps> & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}>>;
export default Assignment;
//# sourceMappingURL=Assignment.d.ts.map