import mongoose from 'mongoose';
const gradeSchema = new mongoose.Schema({
    student: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    course: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course',
        required: true
    },
    assignment: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Assignment'
    },
    score: {
        type: Number,
        required: true,
        min: 0
    },
    maxScore: {
        type: Number,
        required: true,
        min: 1
    },
    percentage: {
        type: Number,
        required: true,
        min: 0,
        max: 100
    },
    grade: {
        type: String,
        enum: ['A', 'B', 'C', 'D', 'F'],
        required: true
    },
    type: {
        type: String,
        enum: ['assignment', 'quiz', 'exam', 'project', 'participation'],
        required: true
    },
    semester: {
        type: String,
        required: true
    },
    academicYear: {
        type: String,
        required: true
    },
    gradedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    gradedAt: {
        type: Date,
        default: Date.now
    },
    comments: {
        type: String
    },
    isFinal: {
        type: Boolean,
        default: false
    },
    curveApplied: {
        type: Boolean,
        default: false
    },
    curveValue: {
        type: Number,
        default: 0
    },
    latePenalty: {
        type: Number,
        default: 0
    },
    extraCredit: {
        type: Number,
        default: 0
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Indexes
gradeSchema.index({ student: 1, course: 1, semester: 1, academicYear: 1 });
gradeSchema.index({ student: 1 });
gradeSchema.index({ course: 1 });
gradeSchema.index({ assignment: 1 });
gradeSchema.index({ gradedBy: 1 });
gradeSchema.index({ gradedAt: -1 });
gradeSchema.index({ type: 1 });
gradeSchema.index({ semester: 1, academicYear: 1 });
// Virtual for letter grade description
gradeSchema.virtual('gradeDescription').get(function () {
    const descriptions = {
        'A': 'Excellent',
        'B': 'Good',
        'C': 'Satisfactory',
        'D': 'Poor',
        'F': 'Failing'
    };
    return descriptions[this.grade] || 'Unknown';
});
// Virtual for grade point
gradeSchema.virtual('gradePoint').get(function () {
    const points = {
        'A': 4.0,
        'B': 3.0,
        'C': 2.0,
        'D': 1.0,
        'F': 0.0
    };
    return points[this.grade] || 0.0;
});
// Pre-save middleware to calculate percentage and letter grade
gradeSchema.pre('save', function (next) {
    // Calculate percentage
    this.percentage = (this.score / this.maxScore) * 100;
    // Determine letter grade based on percentage
    if (this.percentage >= 90) {
        this.grade = 'A';
    }
    else if (this.percentage >= 80) {
        this.grade = 'B';
    }
    else if (this.percentage >= 70) {
        this.grade = 'C';
    }
    else if (this.percentage >= 60) {
        this.grade = 'D';
    }
    else {
        this.grade = 'F';
    }
    next();
});
// Method to apply curve
gradeSchema.methods.applyCurve = function (curveValue) {
    this.curveApplied = true;
    this.curveValue = curveValue;
    this.score = Math.min(this.score + curveValue, this.maxScore);
    return this.save();
};
// Method to apply late penalty
gradeSchema.methods.applyLatePenalty = function (penaltyPercentage) {
    this.latePenalty = penaltyPercentage;
    this.score = this.score * (1 - penaltyPercentage / 100);
    return this.save();
};
// Method to add extra credit
gradeSchema.methods.addExtraCredit = function (extraCredit) {
    this.extraCredit = extraCredit;
    this.score = Math.min(this.score + extraCredit, this.maxScore);
    return this.save();
};
// Static method to find by student
gradeSchema.statics.findByStudent = function (studentId) {
    return this.find({ student: studentId }).populate('course assignment gradedBy');
};
// Static method to find by course
gradeSchema.statics.findByCourse = function (courseId) {
    return this.find({ course: courseId }).populate('student assignment gradedBy');
};
// Static method to find by assignment
gradeSchema.statics.findByAssignment = function (assignmentId) {
    return this.find({ assignment: assignmentId }).populate('student course gradedBy');
};
// Static method to calculate course average
gradeSchema.statics.calculateCourseAverage = function (courseId, studentId) {
    return this.aggregate([
        { $match: { course: mongoose.Types.ObjectId(courseId), student: mongoose.Types.ObjectId(studentId) } },
        { $group: { _id: null, average: { $avg: '$percentage' } } }
    ]);
};
// Static method to get grade distribution
gradeSchema.statics.getGradeDistribution = function (courseId) {
    return this.aggregate([
        { $match: { course: mongoose.Types.ObjectId(courseId) } },
        { $group: { _id: '$grade', count: { $sum: 1 } } },
        { $sort: { _id: 1 } }
    ]);
};
export const Grade = mongoose.model('Grade', gradeSchema);
export default Grade;
//# sourceMappingURL=Grade.js.map