import mongoose from 'mongoose';
export declare const Department: mongoose.Model<{
    name: string;
    isActive: boolean;
    code: string;
    budget: number;
    established: NativeDate;
    programs: mongoose.Types.DocumentArray<{
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }> & {
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }>;
    facilities: string[];
    staff: mongoose.Types.DocumentArray<{
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }> & {
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }>;
    goals: string[];
    achievements: string[];
    partnerships: string[];
    description?: string | null | undefined;
    head?: mongoose.Types.ObjectId | null | undefined;
    location?: {
        room?: string | null | undefined;
        building?: string | null | undefined;
        floor?: string | null | undefined;
    } | null | undefined;
    contact?: {
        email?: string | null | undefined;
        phone?: string | null | undefined;
        fax?: string | null | undefined;
    } | null | undefined;
    website?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    name: string;
    isActive: boolean;
    code: string;
    budget: number;
    established: NativeDate;
    programs: mongoose.Types.DocumentArray<{
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }> & {
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }>;
    facilities: string[];
    staff: mongoose.Types.DocumentArray<{
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }> & {
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }>;
    goals: string[];
    achievements: string[];
    partnerships: string[];
    description?: string | null | undefined;
    head?: mongoose.Types.ObjectId | null | undefined;
    location?: {
        room?: string | null | undefined;
        building?: string | null | undefined;
        floor?: string | null | undefined;
    } | null | undefined;
    contact?: {
        email?: string | null | undefined;
        phone?: string | null | undefined;
        fax?: string | null | undefined;
    } | null | undefined;
    website?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}> & {
    name: string;
    isActive: boolean;
    code: string;
    budget: number;
    established: NativeDate;
    programs: mongoose.Types.DocumentArray<{
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }> & {
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }>;
    facilities: string[];
    staff: mongoose.Types.DocumentArray<{
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }> & {
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }>;
    goals: string[];
    achievements: string[];
    partnerships: string[];
    description?: string | null | undefined;
    head?: mongoose.Types.ObjectId | null | undefined;
    location?: {
        room?: string | null | undefined;
        building?: string | null | undefined;
        floor?: string | null | undefined;
    } | null | undefined;
    contact?: {
        email?: string | null | undefined;
        phone?: string | null | undefined;
        fax?: string | null | undefined;
    } | null | undefined;
    website?: string | null | undefined;
} & mongoose.DefaultTimestampProps & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}, {
    name: string;
    isActive: boolean;
    code: string;
    budget: number;
    established: NativeDate;
    programs: mongoose.Types.DocumentArray<{
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }> & {
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }>;
    facilities: string[];
    staff: mongoose.Types.DocumentArray<{
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }> & {
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }>;
    goals: string[];
    achievements: string[];
    partnerships: string[];
    description?: string | null | undefined;
    head?: mongoose.Types.ObjectId | null | undefined;
    location?: {
        room?: string | null | undefined;
        building?: string | null | undefined;
        floor?: string | null | undefined;
    } | null | undefined;
    contact?: {
        email?: string | null | undefined;
        phone?: string | null | undefined;
        fax?: string | null | undefined;
    } | null | undefined;
    website?: string | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    name: string;
    isActive: boolean;
    code: string;
    budget: number;
    established: NativeDate;
    programs: mongoose.Types.DocumentArray<{
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }> & {
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }>;
    facilities: string[];
    staff: mongoose.Types.DocumentArray<{
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }> & {
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }>;
    goals: string[];
    achievements: string[];
    partnerships: string[];
    description?: string | null | undefined;
    head?: mongoose.Types.ObjectId | null | undefined;
    location?: {
        room?: string | null | undefined;
        building?: string | null | undefined;
        floor?: string | null | undefined;
    } | null | undefined;
    contact?: {
        email?: string | null | undefined;
        phone?: string | null | undefined;
        fax?: string | null | undefined;
    } | null | undefined;
    website?: string | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}>> & mongoose.FlatRecord<{
    name: string;
    isActive: boolean;
    code: string;
    budget: number;
    established: NativeDate;
    programs: mongoose.Types.DocumentArray<{
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }> & {
        level?: string | null | undefined;
        name?: string | null | undefined;
        code?: string | null | undefined;
        description?: string | null | undefined;
        credits?: number | null | undefined;
        duration?: number | null | undefined;
    }>;
    facilities: string[];
    staff: mongoose.Types.DocumentArray<{
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }> & {
        role?: string | null | undefined;
        user?: mongoose.Types.ObjectId | null | undefined;
        startDate?: NativeDate | null | undefined;
        endDate?: NativeDate | null | undefined;
    }>;
    goals: string[];
    achievements: string[];
    partnerships: string[];
    description?: string | null | undefined;
    head?: mongoose.Types.ObjectId | null | undefined;
    location?: {
        room?: string | null | undefined;
        building?: string | null | undefined;
        floor?: string | null | undefined;
    } | null | undefined;
    contact?: {
        email?: string | null | undefined;
        phone?: string | null | undefined;
        fax?: string | null | undefined;
    } | null | undefined;
    website?: string | null | undefined;
} & mongoose.DefaultTimestampProps> & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}>>;
export default Department;
//# sourceMappingURL=Department.d.ts.map