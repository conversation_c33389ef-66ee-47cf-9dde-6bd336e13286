import mongoose from 'mongoose';
const notificationSchema = new mongoose.Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    title: {
        type: String,
        required: true,
        trim: true
    },
    message: {
        type: String,
        required: true,
        trim: true
    },
    type: {
        type: String,
        enum: ['info', 'success', 'warning', 'error'],
        default: 'info'
    },
    link: {
        type: String
    },
    read: {
        type: Boolean,
        default: false
    },
    readAt: {
        type: Date
    },
    metadata: {
        sentBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        sentAt: {
            type: Date,
            default: Date.now
        },
        targetRoles: [String],
        targetDepartments: [String],
        broadcast: Boolean,
        priority: {
            type: String,
            enum: ['low', 'medium', 'high', 'urgent'],
            default: 'medium'
        },
        category: {
            type: String,
            enum: ['system', 'academic', 'financial', 'administrative', 'social'],
            default: 'system'
        },
        expiresAt: Date,
        actionRequired: Boolean,
        actionUrl: String,
        actionText: String
    },
    attachments: [{
            filename: String,
            originalName: String,
            mimetype: String,
            size: Number,
            url: String
        }],
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Indexes
notificationSchema.index({ user: 1 });
notificationSchema.index({ read: 1 });
notificationSchema.index({ type: 1 });
notificationSchema.index({ 'metadata.priority': 1 });
notificationSchema.index({ 'metadata.category': 1 });
notificationSchema.index({ createdAt: -1 });
notificationSchema.index({ 'metadata.expiresAt': 1 }, { expireAfterSeconds: 0 });
// Virtual for time since creation
notificationSchema.virtual('timeSinceCreation').get(function () {
    const now = new Date();
    const diff = now.getTime() - this.createdAt.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    if (days > 0)
        return `${days} day${days > 1 ? 's' : ''} ago`;
    if (hours > 0)
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    if (minutes > 0)
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    return 'Just now';
});
// Virtual for is expired
notificationSchema.virtual('isExpired').get(function () {
    if (this.metadata.expiresAt) {
        return new Date() > this.metadata.expiresAt;
    }
    return false;
});
// Virtual for is urgent
notificationSchema.virtual('isUrgent').get(function () {
    return this.metadata.priority === 'urgent';
});
// Pre-save middleware to set readAt
notificationSchema.pre('save', function (next) {
    if (this.isModified('read') && this.read && !this.readAt) {
        this.readAt = new Date();
    }
    next();
});
// Method to mark as read
notificationSchema.methods.markAsRead = function () {
    this.read = true;
    this.readAt = new Date();
    return this.save();
};
// Method to mark as unread
notificationSchema.methods.markAsUnread = function () {
    this.read = false;
    this.readAt = undefined;
    return this.save();
};
// Method to archive
notificationSchema.methods.archive = function () {
    this.isActive = false;
    return this.save();
};
// Method to extend expiration
notificationSchema.methods.extendExpiration = function (days) {
    const newExpiration = new Date();
    newExpiration.setDate(newExpiration.getDate() + days);
    this.metadata.expiresAt = newExpiration;
    return this.save();
};
// Static method to find by user
notificationSchema.statics.findByUser = function (userId) {
    return this.find({ user: userId, isActive: true }).sort({ createdAt: -1 });
};
// Static method to find unread by user
notificationSchema.statics.findUnreadByUser = function (userId) {
    return this.find({ user: userId, read: false, isActive: true }).sort({ createdAt: -1 });
};
// Static method to find by type
notificationSchema.statics.findByType = function (type) {
    return this.find({ type: type, isActive: true }).sort({ createdAt: -1 });
};
// Static method to find by priority
notificationSchema.statics.findByPriority = function (priority) {
    return this.find({ 'metadata.priority': priority, isActive: true }).sort({ createdAt: -1 });
};
// Static method to find by category
notificationSchema.statics.findByCategory = function (category) {
    return this.find({ 'metadata.category': category, isActive: true }).sort({ createdAt: -1 });
};
// Static method to find expired
notificationSchema.statics.findExpired = function () {
    return this.find({
        'metadata.expiresAt': { $lt: new Date() },
        isActive: true
    });
};
// Static method to get notification statistics
notificationSchema.statics.getStatistics = function (userId) {
    return this.aggregate([
        { $match: { user: mongoose.Types.ObjectId(userId), isActive: true } },
        {
            $group: {
                _id: null,
                total: { $sum: 1 },
                unread: { $sum: { $cond: ['$read', 0, 1] } },
                byType: {
                    $push: {
                        type: '$type',
                        read: '$read'
                    }
                },
                byPriority: {
                    $push: {
                        priority: '$metadata.priority',
                        read: '$read'
                    }
                }
            }
        }
    ]);
};
// Static method to bulk mark as read
notificationSchema.statics.bulkMarkAsRead = function (userId, notificationIds) {
    return this.updateMany({ user: userId, _id: { $in: notificationIds } }, { read: true, readAt: new Date() });
};
// Static method to bulk delete
notificationSchema.statics.bulkDelete = function (userId, notificationIds) {
    return this.updateMany({ user: userId, _id: { $in: notificationIds } }, { isActive: false });
};
export const Notification = mongoose.model('Notification', notificationSchema);
export default Notification;
//# sourceMappingURL=Notification.js.map