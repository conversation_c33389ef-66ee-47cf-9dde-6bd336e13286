import mongoose from 'mongoose';
export declare const Notification: mongoose.Model<{
    message: string;
    type: "info" | "error" | "success" | "warning";
    isActive: boolean;
    user: mongoose.Types.ObjectId;
    read: boolean;
    title: string;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }>;
    link?: string | null | undefined;
    metadata?: {
        targetRoles: string[];
        targetDepartments: string[];
        sentAt: NativeDate;
        priority: "low" | "medium" | "high" | "urgent";
        category: "academic" | "system" | "financial" | "administrative" | "social";
        broadcast?: boolean | null | undefined;
        expiresAt?: NativeDate | null | undefined;
        actionRequired?: boolean | null | undefined;
        actionUrl?: string | null | undefined;
        actionText?: string | null | undefined;
        sentBy?: mongoose.Types.ObjectId | null | undefined;
    } | null | undefined;
    readAt?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    message: string;
    type: "info" | "error" | "success" | "warning";
    isActive: boolean;
    user: mongoose.Types.ObjectId;
    read: boolean;
    title: string;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }>;
    link?: string | null | undefined;
    metadata?: {
        targetRoles: string[];
        targetDepartments: string[];
        sentAt: NativeDate;
        priority: "low" | "medium" | "high" | "urgent";
        category: "academic" | "system" | "financial" | "administrative" | "social";
        broadcast?: boolean | null | undefined;
        expiresAt?: NativeDate | null | undefined;
        actionRequired?: boolean | null | undefined;
        actionUrl?: string | null | undefined;
        actionText?: string | null | undefined;
        sentBy?: mongoose.Types.ObjectId | null | undefined;
    } | null | undefined;
    readAt?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}> & {
    message: string;
    type: "info" | "error" | "success" | "warning";
    isActive: boolean;
    user: mongoose.Types.ObjectId;
    read: boolean;
    title: string;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }>;
    link?: string | null | undefined;
    metadata?: {
        targetRoles: string[];
        targetDepartments: string[];
        sentAt: NativeDate;
        priority: "low" | "medium" | "high" | "urgent";
        category: "academic" | "system" | "financial" | "administrative" | "social";
        broadcast?: boolean | null | undefined;
        expiresAt?: NativeDate | null | undefined;
        actionRequired?: boolean | null | undefined;
        actionUrl?: string | null | undefined;
        actionText?: string | null | undefined;
        sentBy?: mongoose.Types.ObjectId | null | undefined;
    } | null | undefined;
    readAt?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}, {
    message: string;
    type: "info" | "error" | "success" | "warning";
    isActive: boolean;
    user: mongoose.Types.ObjectId;
    read: boolean;
    title: string;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }>;
    link?: string | null | undefined;
    metadata?: {
        targetRoles: string[];
        targetDepartments: string[];
        sentAt: NativeDate;
        priority: "low" | "medium" | "high" | "urgent";
        category: "academic" | "system" | "financial" | "administrative" | "social";
        broadcast?: boolean | null | undefined;
        expiresAt?: NativeDate | null | undefined;
        actionRequired?: boolean | null | undefined;
        actionUrl?: string | null | undefined;
        actionText?: string | null | undefined;
        sentBy?: mongoose.Types.ObjectId | null | undefined;
    } | null | undefined;
    readAt?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    message: string;
    type: "info" | "error" | "success" | "warning";
    isActive: boolean;
    user: mongoose.Types.ObjectId;
    read: boolean;
    title: string;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }>;
    link?: string | null | undefined;
    metadata?: {
        targetRoles: string[];
        targetDepartments: string[];
        sentAt: NativeDate;
        priority: "low" | "medium" | "high" | "urgent";
        category: "academic" | "system" | "financial" | "administrative" | "social";
        broadcast?: boolean | null | undefined;
        expiresAt?: NativeDate | null | undefined;
        actionRequired?: boolean | null | undefined;
        actionUrl?: string | null | undefined;
        actionText?: string | null | undefined;
        sentBy?: mongoose.Types.ObjectId | null | undefined;
    } | null | undefined;
    readAt?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}>> & mongoose.FlatRecord<{
    message: string;
    type: "info" | "error" | "success" | "warning";
    isActive: boolean;
    user: mongoose.Types.ObjectId;
    read: boolean;
    title: string;
    attachments: mongoose.Types.DocumentArray<{
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }, mongoose.Types.Subdocument<mongoose.mongo.BSON.ObjectId, any, {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }> & {
        filename?: string | null | undefined;
        size?: number | null | undefined;
        url?: string | null | undefined;
        originalName?: string | null | undefined;
        mimetype?: string | null | undefined;
    }>;
    link?: string | null | undefined;
    metadata?: {
        targetRoles: string[];
        targetDepartments: string[];
        sentAt: NativeDate;
        priority: "low" | "medium" | "high" | "urgent";
        category: "academic" | "system" | "financial" | "administrative" | "social";
        broadcast?: boolean | null | undefined;
        expiresAt?: NativeDate | null | undefined;
        actionRequired?: boolean | null | undefined;
        actionUrl?: string | null | undefined;
        actionText?: string | null | undefined;
        sentBy?: mongoose.Types.ObjectId | null | undefined;
    } | null | undefined;
    readAt?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps> & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}>>;
export default Notification;
//# sourceMappingURL=Notification.d.ts.map