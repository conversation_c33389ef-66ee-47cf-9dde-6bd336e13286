// Payment integration models and services for the university portal
import mongoose from 'mongoose';
// Payment Methods Schema
const paymentMethodSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true
    },
    code: {
        type: String,
        required: true,
        unique: true,
        trim: true
    },
    provider: {
        type: String,
        required: true,
        enum: ['mpesa', 'jambopay', 'im_bank', 'kcb_buni', 'pesalink']
    },
    isActive: {
        type: Boolean,
        default: true
    },
    config: {
        type: mongoose.Schema.Types.Mixed,
        default: {}
    },
    fees: {
        fixed: {
            type: Number,
            default: 0
        },
        percentage: {
            type: Number,
            default: 0
        }
    },
    limits: {
        min: {
            type: Number,
            default: 1
        },
        max: {
            type: Number,
            default: 150000
        }
    }
}, {
    timestamps: true
});
// Payment Transactions Schema
const paymentTransactionSchema = new mongoose.Schema({
    student: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    fee: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Fee',
        required: true
    },
    amount: {
        type: Number,
        required: true,
        min: 0
    },
    currency: {
        type: String,
        default: 'KES'
    },
    paymentMethod: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'PaymentMethod',
        required: true
    },
    providerTransactionId: {
        type: String,
        sparse: true
    },
    status: {
        type: String,
        enum: ['pending', 'initiated', 'processing', 'completed', 'failed', 'cancelled', 'refunded'],
        default: 'pending'
    },
    referenceNumber: {
        type: String,
        required: true,
        unique: true
    },
    metadata: {
        phoneNumber: String,
        accountNumber: String,
        bankCode: String,
        providerResponse: mongoose.Schema.Types.Mixed,
        callbackData: mongoose.Schema.Types.Mixed,
        retryCount: {
            type: Number,
            default: 0
        },
        maxRetries: {
            type: Number,
            default: 3
        }
    },
    initiatedAt: {
        type: Date,
        default: Date.now
    },
    completedAt: Date,
    failedAt: Date,
    failureReason: String,
    processingFee: {
        type: Number,
        default: 0
    },
    netAmount: {
        type: Number,
        required: true
    }
}, {
    timestamps: true
});
// Payment Webhooks Schema
const paymentWebhookSchema = new mongoose.Schema({
    transaction: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'PaymentTransaction'
    },
    provider: {
        type: String,
        required: true,
        enum: ['mpesa', 'jambopay', 'im_bank', 'kcb_buni', 'pesalink']
    },
    eventType: {
        type: String,
        required: true
    },
    payload: {
        type: mongoose.Schema.Types.Mixed,
        required: true
    },
    signature: String,
    processed: {
        type: Boolean,
        default: false
    },
    processedAt: Date,
    processingAttempts: {
        type: Number,
        default: 0
    },
    maxProcessingAttempts: {
        type: Number,
        default: 5
    }
}, {
    timestamps: true
});
// Payment Reconciliation Schema
const paymentReconciliationSchema = new mongoose.Schema({
    transaction: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'PaymentTransaction',
        required: true
    },
    providerTransactionId: {
        type: String,
        required: true
    },
    amount: {
        type: Number,
        required: true
    },
    status: {
        type: String,
        required: true,
        enum: ['matched', 'discrepancy', 'missing', 'duplicate']
    },
    reconciledAt: {
        type: Date,
        default: Date.now
    },
    notes: String,
    discrepancyDetails: {
        amountDifference: Number,
        statusDifference: String,
        timestampDifference: Number
    }
}, {
    timestamps: true
});
// Fee Schema (if not already exists)
const feeSchema = new mongoose.Schema({
    student: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    course: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course'
    },
    semester: {
        type: String,
        required: true
    },
    academicYear: {
        type: String,
        required: true
    },
    feeType: {
        type: String,
        required: true,
        enum: ['tuition', 'registration', 'examination', 'library', 'hostel', 'other']
    },
    amount: {
        type: Number,
        required: true,
        min: 0
    },
    description: {
        type: String,
        trim: true
    },
    dueDate: {
        type: Date,
        required: true
    },
    isPaid: {
        type: Boolean,
        default: false
    },
    paidAt: Date,
    paymentTransaction: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'PaymentTransaction'
    },
    lateFee: {
        type: Number,
        default: 0
    },
    totalAmount: {
        type: Number,
        required: true
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});
// Indexes for performance
paymentTransactionSchema.index({ student: 1, status: 1 });
paymentTransactionSchema.index({ referenceNumber: 1 });
paymentTransactionSchema.index({ providerTransactionId: 1 });
paymentTransactionSchema.index({ initiatedAt: -1 });
paymentTransactionSchema.index({ status: 1, initiatedAt: -1 });
paymentWebhookSchema.index({ provider: 1, processed: 1 });
paymentWebhookSchema.index({ transaction: 1 });
paymentWebhookSchema.index({ createdAt: -1 });
paymentReconciliationSchema.index({ transaction: 1 });
paymentReconciliationSchema.index({ providerTransactionId: 1 });
paymentReconciliationSchema.index({ reconciledAt: -1 });
feeSchema.index({ student: 1, semester: 1, academicYear: 1 });
feeSchema.index({ dueDate: 1 });
feeSchema.index({ isPaid: 1 });
// Virtual for payment status
paymentTransactionSchema.virtual('isSuccessful').get(function () {
    return this.status === 'completed';
});
paymentTransactionSchema.virtual('isFailed').get(function () {
    return ['failed', 'cancelled'].includes(this.status);
});
paymentTransactionSchema.virtual('isPending').get(function () {
    return ['pending', 'initiated', 'processing'].includes(this.status);
});
// Methods
paymentTransactionSchema.methods.canRetry = function () {
    return this.metadata.retryCount < this.metadata.maxRetries && this.isFailed;
};
paymentTransactionSchema.methods.incrementRetry = function () {
    this.metadata.retryCount += 1;
    return this.save();
};
paymentTransactionSchema.methods.markAsCompleted = function (providerData) {
    this.status = 'completed';
    this.completedAt = new Date();
    this.metadata.providerResponse = providerData;
    return this.save();
};
paymentTransactionSchema.methods.markAsFailed = function (reason) {
    this.status = 'failed';
    this.failedAt = new Date();
    this.failureReason = reason;
    return this.save();
};
// Static methods
paymentTransactionSchema.statics.findByReference = function (referenceNumber) {
    return this.findOne({ referenceNumber });
};
paymentTransactionSchema.statics.findByProviderTransactionId = function (providerTransactionId) {
    return this.findOne({ providerTransactionId });
};
paymentTransactionSchema.statics.findPendingTransactions = function () {
    return this.find({ status: { $in: ['pending', 'initiated', 'processing'] } });
};
paymentTransactionSchema.statics.findFailedTransactions = function () {
    return this.find({ status: 'failed' });
};
// Create models
export const PaymentMethod = mongoose.model('PaymentMethod', paymentMethodSchema);
export const PaymentTransaction = mongoose.model('PaymentTransaction', paymentTransactionSchema);
export const PaymentWebhook = mongoose.model('PaymentWebhook', paymentWebhookSchema);
export const PaymentReconciliation = mongoose.model('PaymentReconciliation', paymentReconciliationSchema);
export const Fee = mongoose.model('Fee', feeSchema);
export default {
    PaymentMethod,
    PaymentTransaction,
    PaymentWebhook,
    PaymentReconciliation,
    Fee
};
//# sourceMappingURL=Payment.js.map