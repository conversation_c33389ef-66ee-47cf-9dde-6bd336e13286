import mongoose from 'mongoose';
export declare const Session: mongoose.Model<{
    type: "lecture" | "lab" | "tutorial" | "seminar";
    isActive: boolean;
    lecturer: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    date: NativeDate;
    startTime: string;
    endTime: string;
    attendanceTaken: boolean;
    objectives: string[];
    materials: string[];
    description?: string | null | undefined;
    notes?: string | null | undefined;
    room?: string | null | undefined;
    topic?: string | null | undefined;
    attendanceTakenAt?: NativeDate | null | undefined;
    attendanceTakenBy?: mongoose.Types.ObjectId | null | undefined;
    homework?: string | null | undefined;
    nextSession?: mongoose.Types.ObjectId | null | undefined;
    previousSession?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    type: "lecture" | "lab" | "tutorial" | "seminar";
    isActive: boolean;
    lecturer: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    date: NativeDate;
    startTime: string;
    endTime: string;
    attendanceTaken: boolean;
    objectives: string[];
    materials: string[];
    description?: string | null | undefined;
    notes?: string | null | undefined;
    room?: string | null | undefined;
    topic?: string | null | undefined;
    attendanceTakenAt?: NativeDate | null | undefined;
    attendanceTakenBy?: mongoose.Types.ObjectId | null | undefined;
    homework?: string | null | undefined;
    nextSession?: mongoose.Types.ObjectId | null | undefined;
    previousSession?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}> & {
    type: "lecture" | "lab" | "tutorial" | "seminar";
    isActive: boolean;
    lecturer: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    date: NativeDate;
    startTime: string;
    endTime: string;
    attendanceTaken: boolean;
    objectives: string[];
    materials: string[];
    description?: string | null | undefined;
    notes?: string | null | undefined;
    room?: string | null | undefined;
    topic?: string | null | undefined;
    attendanceTakenAt?: NativeDate | null | undefined;
    attendanceTakenBy?: mongoose.Types.ObjectId | null | undefined;
    homework?: string | null | undefined;
    nextSession?: mongoose.Types.ObjectId | null | undefined;
    previousSession?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}, {
    type: "lecture" | "lab" | "tutorial" | "seminar";
    isActive: boolean;
    lecturer: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    date: NativeDate;
    startTime: string;
    endTime: string;
    attendanceTaken: boolean;
    objectives: string[];
    materials: string[];
    description?: string | null | undefined;
    notes?: string | null | undefined;
    room?: string | null | undefined;
    topic?: string | null | undefined;
    attendanceTakenAt?: NativeDate | null | undefined;
    attendanceTakenBy?: mongoose.Types.ObjectId | null | undefined;
    homework?: string | null | undefined;
    nextSession?: mongoose.Types.ObjectId | null | undefined;
    previousSession?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    type: "lecture" | "lab" | "tutorial" | "seminar";
    isActive: boolean;
    lecturer: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    date: NativeDate;
    startTime: string;
    endTime: string;
    attendanceTaken: boolean;
    objectives: string[];
    materials: string[];
    description?: string | null | undefined;
    notes?: string | null | undefined;
    room?: string | null | undefined;
    topic?: string | null | undefined;
    attendanceTakenAt?: NativeDate | null | undefined;
    attendanceTakenBy?: mongoose.Types.ObjectId | null | undefined;
    homework?: string | null | undefined;
    nextSession?: mongoose.Types.ObjectId | null | undefined;
    previousSession?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}>> & mongoose.FlatRecord<{
    type: "lecture" | "lab" | "tutorial" | "seminar";
    isActive: boolean;
    lecturer: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    date: NativeDate;
    startTime: string;
    endTime: string;
    attendanceTaken: boolean;
    objectives: string[];
    materials: string[];
    description?: string | null | undefined;
    notes?: string | null | undefined;
    room?: string | null | undefined;
    topic?: string | null | undefined;
    attendanceTakenAt?: NativeDate | null | undefined;
    attendanceTakenBy?: mongoose.Types.ObjectId | null | undefined;
    homework?: string | null | undefined;
    nextSession?: mongoose.Types.ObjectId | null | undefined;
    previousSession?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps> & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}>>;
export default Session;
//# sourceMappingURL=Session.d.ts.map