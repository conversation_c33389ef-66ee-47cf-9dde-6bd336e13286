import mongoose from 'mongoose';
export declare const PaymentMethod: mongoose.Model<{
    name: string;
    isActive: boolean;
    code: string;
    provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
    config: any;
    fees?: {
        fixed: number;
        percentage: number;
    } | null | undefined;
    limits?: {
        min: number;
        max: number;
    } | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    name: string;
    isActive: boolean;
    code: string;
    provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
    config: any;
    fees?: {
        fixed: number;
        percentage: number;
    } | null | undefined;
    limits?: {
        min: number;
        max: number;
    } | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
}> & {
    name: string;
    isActive: boolean;
    code: string;
    provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
    config: any;
    fees?: {
        fixed: number;
        percentage: number;
    } | null | undefined;
    limits?: {
        min: number;
        max: number;
    } | null | undefined;
} & mongoose.DefaultTimestampProps & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
}, {
    name: string;
    isActive: boolean;
    code: string;
    provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
    config: any;
    fees?: {
        fixed: number;
        percentage: number;
    } | null | undefined;
    limits?: {
        min: number;
        max: number;
    } | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    name: string;
    isActive: boolean;
    code: string;
    provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
    config: any;
    fees?: {
        fixed: number;
        percentage: number;
    } | null | undefined;
    limits?: {
        min: number;
        max: number;
    } | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
}>> & mongoose.FlatRecord<{
    name: string;
    isActive: boolean;
    code: string;
    provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
    config: any;
    fees?: {
        fixed: number;
        percentage: number;
    } | null | undefined;
    limits?: {
        min: number;
        max: number;
    } | null | undefined;
} & mongoose.DefaultTimestampProps> & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}>>;
export declare const PaymentTransaction: mongoose.Model<{
    student: mongoose.Types.ObjectId;
    fee: mongoose.Types.ObjectId;
    amount: number;
    currency: string;
    paymentMethod: mongoose.Types.ObjectId;
    status: "pending" | "initiated" | "processing" | "completed" | "failed" | "cancelled" | "refunded";
    referenceNumber: string;
    initiatedAt: NativeDate;
    processingFee: number;
    netAmount: number;
    completedAt?: NativeDate | null | undefined;
    failedAt?: NativeDate | null | undefined;
    failureReason?: string | null | undefined;
    providerTransactionId?: string | null | undefined;
    metadata?: {
        retryCount: number;
        maxRetries: number;
        phoneNumber?: string | null | undefined;
        accountNumber?: string | null | undefined;
        bankCode?: string | null | undefined;
        providerResponse?: any;
        callbackData?: any;
    } | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    student: mongoose.Types.ObjectId;
    fee: mongoose.Types.ObjectId;
    amount: number;
    currency: string;
    paymentMethod: mongoose.Types.ObjectId;
    status: "pending" | "initiated" | "processing" | "completed" | "failed" | "cancelled" | "refunded";
    referenceNumber: string;
    initiatedAt: NativeDate;
    processingFee: number;
    netAmount: number;
    completedAt?: NativeDate | null | undefined;
    failedAt?: NativeDate | null | undefined;
    failureReason?: string | null | undefined;
    providerTransactionId?: string | null | undefined;
    metadata?: {
        retryCount: number;
        maxRetries: number;
        phoneNumber?: string | null | undefined;
        accountNumber?: string | null | undefined;
        bankCode?: string | null | undefined;
        providerResponse?: any;
        callbackData?: any;
    } | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
}> & {
    student: mongoose.Types.ObjectId;
    fee: mongoose.Types.ObjectId;
    amount: number;
    currency: string;
    paymentMethod: mongoose.Types.ObjectId;
    status: "pending" | "initiated" | "processing" | "completed" | "failed" | "cancelled" | "refunded";
    referenceNumber: string;
    initiatedAt: NativeDate;
    processingFee: number;
    netAmount: number;
    completedAt?: NativeDate | null | undefined;
    failedAt?: NativeDate | null | undefined;
    failureReason?: string | null | undefined;
    providerTransactionId?: string | null | undefined;
    metadata?: {
        retryCount: number;
        maxRetries: number;
        phoneNumber?: string | null | undefined;
        accountNumber?: string | null | undefined;
        bankCode?: string | null | undefined;
        providerResponse?: any;
        callbackData?: any;
    } | null | undefined;
} & mongoose.DefaultTimestampProps & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
}, {
    student: mongoose.Types.ObjectId;
    fee: mongoose.Types.ObjectId;
    amount: number;
    currency: string;
    paymentMethod: mongoose.Types.ObjectId;
    status: "pending" | "initiated" | "processing" | "completed" | "failed" | "cancelled" | "refunded";
    referenceNumber: string;
    initiatedAt: NativeDate;
    processingFee: number;
    netAmount: number;
    completedAt?: NativeDate | null | undefined;
    failedAt?: NativeDate | null | undefined;
    failureReason?: string | null | undefined;
    providerTransactionId?: string | null | undefined;
    metadata?: {
        retryCount: number;
        maxRetries: number;
        phoneNumber?: string | null | undefined;
        accountNumber?: string | null | undefined;
        bankCode?: string | null | undefined;
        providerResponse?: any;
        callbackData?: any;
    } | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    student: mongoose.Types.ObjectId;
    fee: mongoose.Types.ObjectId;
    amount: number;
    currency: string;
    paymentMethod: mongoose.Types.ObjectId;
    status: "pending" | "initiated" | "processing" | "completed" | "failed" | "cancelled" | "refunded";
    referenceNumber: string;
    initiatedAt: NativeDate;
    processingFee: number;
    netAmount: number;
    completedAt?: NativeDate | null | undefined;
    failedAt?: NativeDate | null | undefined;
    failureReason?: string | null | undefined;
    providerTransactionId?: string | null | undefined;
    metadata?: {
        retryCount: number;
        maxRetries: number;
        phoneNumber?: string | null | undefined;
        accountNumber?: string | null | undefined;
        bankCode?: string | null | undefined;
        providerResponse?: any;
        callbackData?: any;
    } | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
}>> & mongoose.FlatRecord<{
    student: mongoose.Types.ObjectId;
    fee: mongoose.Types.ObjectId;
    amount: number;
    currency: string;
    paymentMethod: mongoose.Types.ObjectId;
    status: "pending" | "initiated" | "processing" | "completed" | "failed" | "cancelled" | "refunded";
    referenceNumber: string;
    initiatedAt: NativeDate;
    processingFee: number;
    netAmount: number;
    completedAt?: NativeDate | null | undefined;
    failedAt?: NativeDate | null | undefined;
    failureReason?: string | null | undefined;
    providerTransactionId?: string | null | undefined;
    metadata?: {
        retryCount: number;
        maxRetries: number;
        phoneNumber?: string | null | undefined;
        accountNumber?: string | null | undefined;
        bankCode?: string | null | undefined;
        providerResponse?: any;
        callbackData?: any;
    } | null | undefined;
} & mongoose.DefaultTimestampProps> & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}>>;
export declare const PaymentWebhook: mongoose.Model<{
    provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
    eventType: string;
    payload: any;
    processed: boolean;
    processingAttempts: number;
    maxProcessingAttempts: number;
    signature?: string | null | undefined;
    processedAt?: NativeDate | null | undefined;
    transaction?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
    eventType: string;
    payload: any;
    processed: boolean;
    processingAttempts: number;
    maxProcessingAttempts: number;
    signature?: string | null | undefined;
    processedAt?: NativeDate | null | undefined;
    transaction?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
}> & {
    provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
    eventType: string;
    payload: any;
    processed: boolean;
    processingAttempts: number;
    maxProcessingAttempts: number;
    signature?: string | null | undefined;
    processedAt?: NativeDate | null | undefined;
    transaction?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
}, {
    provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
    eventType: string;
    payload: any;
    processed: boolean;
    processingAttempts: number;
    maxProcessingAttempts: number;
    signature?: string | null | undefined;
    processedAt?: NativeDate | null | undefined;
    transaction?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
    eventType: string;
    payload: any;
    processed: boolean;
    processingAttempts: number;
    maxProcessingAttempts: number;
    signature?: string | null | undefined;
    processedAt?: NativeDate | null | undefined;
    transaction?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
}>> & mongoose.FlatRecord<{
    provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
    eventType: string;
    payload: any;
    processed: boolean;
    processingAttempts: number;
    maxProcessingAttempts: number;
    signature?: string | null | undefined;
    processedAt?: NativeDate | null | undefined;
    transaction?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps> & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}>>;
export declare const PaymentReconciliation: mongoose.Model<{
    amount: number;
    providerTransactionId: string;
    status: "matched" | "discrepancy" | "missing" | "duplicate";
    transaction: mongoose.Types.ObjectId;
    reconciledAt: NativeDate;
    notes?: string | null | undefined;
    discrepancyDetails?: {
        amountDifference?: number | null | undefined;
        statusDifference?: string | null | undefined;
        timestampDifference?: number | null | undefined;
    } | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    amount: number;
    providerTransactionId: string;
    status: "matched" | "discrepancy" | "missing" | "duplicate";
    transaction: mongoose.Types.ObjectId;
    reconciledAt: NativeDate;
    notes?: string | null | undefined;
    discrepancyDetails?: {
        amountDifference?: number | null | undefined;
        statusDifference?: string | null | undefined;
        timestampDifference?: number | null | undefined;
    } | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
}> & {
    amount: number;
    providerTransactionId: string;
    status: "matched" | "discrepancy" | "missing" | "duplicate";
    transaction: mongoose.Types.ObjectId;
    reconciledAt: NativeDate;
    notes?: string | null | undefined;
    discrepancyDetails?: {
        amountDifference?: number | null | undefined;
        statusDifference?: string | null | undefined;
        timestampDifference?: number | null | undefined;
    } | null | undefined;
} & mongoose.DefaultTimestampProps & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
}, {
    amount: number;
    providerTransactionId: string;
    status: "matched" | "discrepancy" | "missing" | "duplicate";
    transaction: mongoose.Types.ObjectId;
    reconciledAt: NativeDate;
    notes?: string | null | undefined;
    discrepancyDetails?: {
        amountDifference?: number | null | undefined;
        statusDifference?: string | null | undefined;
        timestampDifference?: number | null | undefined;
    } | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    amount: number;
    providerTransactionId: string;
    status: "matched" | "discrepancy" | "missing" | "duplicate";
    transaction: mongoose.Types.ObjectId;
    reconciledAt: NativeDate;
    notes?: string | null | undefined;
    discrepancyDetails?: {
        amountDifference?: number | null | undefined;
        statusDifference?: string | null | undefined;
        timestampDifference?: number | null | undefined;
    } | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
}>> & mongoose.FlatRecord<{
    amount: number;
    providerTransactionId: string;
    status: "matched" | "discrepancy" | "missing" | "duplicate";
    transaction: mongoose.Types.ObjectId;
    reconciledAt: NativeDate;
    notes?: string | null | undefined;
    discrepancyDetails?: {
        amountDifference?: number | null | undefined;
        statusDifference?: string | null | undefined;
        timestampDifference?: number | null | undefined;
    } | null | undefined;
} & mongoose.DefaultTimestampProps> & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}>>;
export declare const Fee: mongoose.Model<{
    isActive: boolean;
    semester: string;
    student: mongoose.Types.ObjectId;
    dueDate: NativeDate;
    amount: number;
    academicYear: string;
    feeType: "tuition" | "registration" | "examination" | "library" | "hostel" | "other";
    isPaid: boolean;
    lateFee: number;
    totalAmount: number;
    course?: mongoose.Types.ObjectId | null | undefined;
    description?: string | null | undefined;
    paidAt?: NativeDate | null | undefined;
    paymentTransaction?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    isActive: boolean;
    semester: string;
    student: mongoose.Types.ObjectId;
    dueDate: NativeDate;
    amount: number;
    academicYear: string;
    feeType: "tuition" | "registration" | "examination" | "library" | "hostel" | "other";
    isPaid: boolean;
    lateFee: number;
    totalAmount: number;
    course?: mongoose.Types.ObjectId | null | undefined;
    description?: string | null | undefined;
    paidAt?: NativeDate | null | undefined;
    paymentTransaction?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
}> & {
    isActive: boolean;
    semester: string;
    student: mongoose.Types.ObjectId;
    dueDate: NativeDate;
    amount: number;
    academicYear: string;
    feeType: "tuition" | "registration" | "examination" | "library" | "hostel" | "other";
    isPaid: boolean;
    lateFee: number;
    totalAmount: number;
    course?: mongoose.Types.ObjectId | null | undefined;
    description?: string | null | undefined;
    paidAt?: NativeDate | null | undefined;
    paymentTransaction?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
}, {
    isActive: boolean;
    semester: string;
    student: mongoose.Types.ObjectId;
    dueDate: NativeDate;
    amount: number;
    academicYear: string;
    feeType: "tuition" | "registration" | "examination" | "library" | "hostel" | "other";
    isPaid: boolean;
    lateFee: number;
    totalAmount: number;
    course?: mongoose.Types.ObjectId | null | undefined;
    description?: string | null | undefined;
    paidAt?: NativeDate | null | undefined;
    paymentTransaction?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    isActive: boolean;
    semester: string;
    student: mongoose.Types.ObjectId;
    dueDate: NativeDate;
    amount: number;
    academicYear: string;
    feeType: "tuition" | "registration" | "examination" | "library" | "hostel" | "other";
    isPaid: boolean;
    lateFee: number;
    totalAmount: number;
    course?: mongoose.Types.ObjectId | null | undefined;
    description?: string | null | undefined;
    paidAt?: NativeDate | null | undefined;
    paymentTransaction?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
}>> & mongoose.FlatRecord<{
    isActive: boolean;
    semester: string;
    student: mongoose.Types.ObjectId;
    dueDate: NativeDate;
    amount: number;
    academicYear: string;
    feeType: "tuition" | "registration" | "examination" | "library" | "hostel" | "other";
    isPaid: boolean;
    lateFee: number;
    totalAmount: number;
    course?: mongoose.Types.ObjectId | null | undefined;
    description?: string | null | undefined;
    paidAt?: NativeDate | null | undefined;
    paymentTransaction?: mongoose.Types.ObjectId | null | undefined;
} & mongoose.DefaultTimestampProps> & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}>>;
declare const _default: {
    PaymentMethod: mongoose.Model<{
        name: string;
        isActive: boolean;
        code: string;
        provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
        config: any;
        fees?: {
            fixed: number;
            percentage: number;
        } | null | undefined;
        limits?: {
            min: number;
            max: number;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
        name: string;
        isActive: boolean;
        code: string;
        provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
        config: any;
        fees?: {
            fixed: number;
            percentage: number;
        } | null | undefined;
        limits?: {
            min: number;
            max: number;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps, {}, {
        timestamps: true;
    }> & {
        name: string;
        isActive: boolean;
        code: string;
        provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
        config: any;
        fees?: {
            fixed: number;
            percentage: number;
        } | null | undefined;
        limits?: {
            min: number;
            max: number;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps & {
        _id: mongoose.Types.ObjectId;
    } & {
        __v: number;
    }, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
        timestamps: true;
    }, {
        name: string;
        isActive: boolean;
        code: string;
        provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
        config: any;
        fees?: {
            fixed: number;
            percentage: number;
        } | null | undefined;
        limits?: {
            min: number;
            max: number;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
        name: string;
        isActive: boolean;
        code: string;
        provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
        config: any;
        fees?: {
            fixed: number;
            percentage: number;
        } | null | undefined;
        limits?: {
            min: number;
            max: number;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
        timestamps: true;
    }>> & mongoose.FlatRecord<{
        name: string;
        isActive: boolean;
        code: string;
        provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
        config: any;
        fees?: {
            fixed: number;
            percentage: number;
        } | null | undefined;
        limits?: {
            min: number;
            max: number;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps> & {
        _id: mongoose.Types.ObjectId;
    } & {
        __v: number;
    }>>;
    PaymentTransaction: mongoose.Model<{
        student: mongoose.Types.ObjectId;
        fee: mongoose.Types.ObjectId;
        amount: number;
        currency: string;
        paymentMethod: mongoose.Types.ObjectId;
        status: "pending" | "initiated" | "processing" | "completed" | "failed" | "cancelled" | "refunded";
        referenceNumber: string;
        initiatedAt: NativeDate;
        processingFee: number;
        netAmount: number;
        completedAt?: NativeDate | null | undefined;
        failedAt?: NativeDate | null | undefined;
        failureReason?: string | null | undefined;
        providerTransactionId?: string | null | undefined;
        metadata?: {
            retryCount: number;
            maxRetries: number;
            phoneNumber?: string | null | undefined;
            accountNumber?: string | null | undefined;
            bankCode?: string | null | undefined;
            providerResponse?: any;
            callbackData?: any;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
        student: mongoose.Types.ObjectId;
        fee: mongoose.Types.ObjectId;
        amount: number;
        currency: string;
        paymentMethod: mongoose.Types.ObjectId;
        status: "pending" | "initiated" | "processing" | "completed" | "failed" | "cancelled" | "refunded";
        referenceNumber: string;
        initiatedAt: NativeDate;
        processingFee: number;
        netAmount: number;
        completedAt?: NativeDate | null | undefined;
        failedAt?: NativeDate | null | undefined;
        failureReason?: string | null | undefined;
        providerTransactionId?: string | null | undefined;
        metadata?: {
            retryCount: number;
            maxRetries: number;
            phoneNumber?: string | null | undefined;
            accountNumber?: string | null | undefined;
            bankCode?: string | null | undefined;
            providerResponse?: any;
            callbackData?: any;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps, {}, {
        timestamps: true;
    }> & {
        student: mongoose.Types.ObjectId;
        fee: mongoose.Types.ObjectId;
        amount: number;
        currency: string;
        paymentMethod: mongoose.Types.ObjectId;
        status: "pending" | "initiated" | "processing" | "completed" | "failed" | "cancelled" | "refunded";
        referenceNumber: string;
        initiatedAt: NativeDate;
        processingFee: number;
        netAmount: number;
        completedAt?: NativeDate | null | undefined;
        failedAt?: NativeDate | null | undefined;
        failureReason?: string | null | undefined;
        providerTransactionId?: string | null | undefined;
        metadata?: {
            retryCount: number;
            maxRetries: number;
            phoneNumber?: string | null | undefined;
            accountNumber?: string | null | undefined;
            bankCode?: string | null | undefined;
            providerResponse?: any;
            callbackData?: any;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps & {
        _id: mongoose.Types.ObjectId;
    } & {
        __v: number;
    }, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
        timestamps: true;
    }, {
        student: mongoose.Types.ObjectId;
        fee: mongoose.Types.ObjectId;
        amount: number;
        currency: string;
        paymentMethod: mongoose.Types.ObjectId;
        status: "pending" | "initiated" | "processing" | "completed" | "failed" | "cancelled" | "refunded";
        referenceNumber: string;
        initiatedAt: NativeDate;
        processingFee: number;
        netAmount: number;
        completedAt?: NativeDate | null | undefined;
        failedAt?: NativeDate | null | undefined;
        failureReason?: string | null | undefined;
        providerTransactionId?: string | null | undefined;
        metadata?: {
            retryCount: number;
            maxRetries: number;
            phoneNumber?: string | null | undefined;
            accountNumber?: string | null | undefined;
            bankCode?: string | null | undefined;
            providerResponse?: any;
            callbackData?: any;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
        student: mongoose.Types.ObjectId;
        fee: mongoose.Types.ObjectId;
        amount: number;
        currency: string;
        paymentMethod: mongoose.Types.ObjectId;
        status: "pending" | "initiated" | "processing" | "completed" | "failed" | "cancelled" | "refunded";
        referenceNumber: string;
        initiatedAt: NativeDate;
        processingFee: number;
        netAmount: number;
        completedAt?: NativeDate | null | undefined;
        failedAt?: NativeDate | null | undefined;
        failureReason?: string | null | undefined;
        providerTransactionId?: string | null | undefined;
        metadata?: {
            retryCount: number;
            maxRetries: number;
            phoneNumber?: string | null | undefined;
            accountNumber?: string | null | undefined;
            bankCode?: string | null | undefined;
            providerResponse?: any;
            callbackData?: any;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
        timestamps: true;
    }>> & mongoose.FlatRecord<{
        student: mongoose.Types.ObjectId;
        fee: mongoose.Types.ObjectId;
        amount: number;
        currency: string;
        paymentMethod: mongoose.Types.ObjectId;
        status: "pending" | "initiated" | "processing" | "completed" | "failed" | "cancelled" | "refunded";
        referenceNumber: string;
        initiatedAt: NativeDate;
        processingFee: number;
        netAmount: number;
        completedAt?: NativeDate | null | undefined;
        failedAt?: NativeDate | null | undefined;
        failureReason?: string | null | undefined;
        providerTransactionId?: string | null | undefined;
        metadata?: {
            retryCount: number;
            maxRetries: number;
            phoneNumber?: string | null | undefined;
            accountNumber?: string | null | undefined;
            bankCode?: string | null | undefined;
            providerResponse?: any;
            callbackData?: any;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps> & {
        _id: mongoose.Types.ObjectId;
    } & {
        __v: number;
    }>>;
    PaymentWebhook: mongoose.Model<{
        provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
        eventType: string;
        payload: any;
        processed: boolean;
        processingAttempts: number;
        maxProcessingAttempts: number;
        signature?: string | null | undefined;
        processedAt?: NativeDate | null | undefined;
        transaction?: mongoose.Types.ObjectId | null | undefined;
    } & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
        provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
        eventType: string;
        payload: any;
        processed: boolean;
        processingAttempts: number;
        maxProcessingAttempts: number;
        signature?: string | null | undefined;
        processedAt?: NativeDate | null | undefined;
        transaction?: mongoose.Types.ObjectId | null | undefined;
    } & mongoose.DefaultTimestampProps, {}, {
        timestamps: true;
    }> & {
        provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
        eventType: string;
        payload: any;
        processed: boolean;
        processingAttempts: number;
        maxProcessingAttempts: number;
        signature?: string | null | undefined;
        processedAt?: NativeDate | null | undefined;
        transaction?: mongoose.Types.ObjectId | null | undefined;
    } & mongoose.DefaultTimestampProps & {
        _id: mongoose.Types.ObjectId;
    } & {
        __v: number;
    }, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
        timestamps: true;
    }, {
        provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
        eventType: string;
        payload: any;
        processed: boolean;
        processingAttempts: number;
        maxProcessingAttempts: number;
        signature?: string | null | undefined;
        processedAt?: NativeDate | null | undefined;
        transaction?: mongoose.Types.ObjectId | null | undefined;
    } & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
        provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
        eventType: string;
        payload: any;
        processed: boolean;
        processingAttempts: number;
        maxProcessingAttempts: number;
        signature?: string | null | undefined;
        processedAt?: NativeDate | null | undefined;
        transaction?: mongoose.Types.ObjectId | null | undefined;
    } & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
        timestamps: true;
    }>> & mongoose.FlatRecord<{
        provider: "mpesa" | "jambopay" | "im_bank" | "kcb_buni" | "pesalink";
        eventType: string;
        payload: any;
        processed: boolean;
        processingAttempts: number;
        maxProcessingAttempts: number;
        signature?: string | null | undefined;
        processedAt?: NativeDate | null | undefined;
        transaction?: mongoose.Types.ObjectId | null | undefined;
    } & mongoose.DefaultTimestampProps> & {
        _id: mongoose.Types.ObjectId;
    } & {
        __v: number;
    }>>;
    PaymentReconciliation: mongoose.Model<{
        amount: number;
        providerTransactionId: string;
        status: "matched" | "discrepancy" | "missing" | "duplicate";
        transaction: mongoose.Types.ObjectId;
        reconciledAt: NativeDate;
        notes?: string | null | undefined;
        discrepancyDetails?: {
            amountDifference?: number | null | undefined;
            statusDifference?: string | null | undefined;
            timestampDifference?: number | null | undefined;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
        amount: number;
        providerTransactionId: string;
        status: "matched" | "discrepancy" | "missing" | "duplicate";
        transaction: mongoose.Types.ObjectId;
        reconciledAt: NativeDate;
        notes?: string | null | undefined;
        discrepancyDetails?: {
            amountDifference?: number | null | undefined;
            statusDifference?: string | null | undefined;
            timestampDifference?: number | null | undefined;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps, {}, {
        timestamps: true;
    }> & {
        amount: number;
        providerTransactionId: string;
        status: "matched" | "discrepancy" | "missing" | "duplicate";
        transaction: mongoose.Types.ObjectId;
        reconciledAt: NativeDate;
        notes?: string | null | undefined;
        discrepancyDetails?: {
            amountDifference?: number | null | undefined;
            statusDifference?: string | null | undefined;
            timestampDifference?: number | null | undefined;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps & {
        _id: mongoose.Types.ObjectId;
    } & {
        __v: number;
    }, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
        timestamps: true;
    }, {
        amount: number;
        providerTransactionId: string;
        status: "matched" | "discrepancy" | "missing" | "duplicate";
        transaction: mongoose.Types.ObjectId;
        reconciledAt: NativeDate;
        notes?: string | null | undefined;
        discrepancyDetails?: {
            amountDifference?: number | null | undefined;
            statusDifference?: string | null | undefined;
            timestampDifference?: number | null | undefined;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
        amount: number;
        providerTransactionId: string;
        status: "matched" | "discrepancy" | "missing" | "duplicate";
        transaction: mongoose.Types.ObjectId;
        reconciledAt: NativeDate;
        notes?: string | null | undefined;
        discrepancyDetails?: {
            amountDifference?: number | null | undefined;
            statusDifference?: string | null | undefined;
            timestampDifference?: number | null | undefined;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
        timestamps: true;
    }>> & mongoose.FlatRecord<{
        amount: number;
        providerTransactionId: string;
        status: "matched" | "discrepancy" | "missing" | "duplicate";
        transaction: mongoose.Types.ObjectId;
        reconciledAt: NativeDate;
        notes?: string | null | undefined;
        discrepancyDetails?: {
            amountDifference?: number | null | undefined;
            statusDifference?: string | null | undefined;
            timestampDifference?: number | null | undefined;
        } | null | undefined;
    } & mongoose.DefaultTimestampProps> & {
        _id: mongoose.Types.ObjectId;
    } & {
        __v: number;
    }>>;
    Fee: mongoose.Model<{
        isActive: boolean;
        semester: string;
        student: mongoose.Types.ObjectId;
        dueDate: NativeDate;
        amount: number;
        academicYear: string;
        feeType: "tuition" | "registration" | "examination" | "library" | "hostel" | "other";
        isPaid: boolean;
        lateFee: number;
        totalAmount: number;
        course?: mongoose.Types.ObjectId | null | undefined;
        description?: string | null | undefined;
        paidAt?: NativeDate | null | undefined;
        paymentTransaction?: mongoose.Types.ObjectId | null | undefined;
    } & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
        isActive: boolean;
        semester: string;
        student: mongoose.Types.ObjectId;
        dueDate: NativeDate;
        amount: number;
        academicYear: string;
        feeType: "tuition" | "registration" | "examination" | "library" | "hostel" | "other";
        isPaid: boolean;
        lateFee: number;
        totalAmount: number;
        course?: mongoose.Types.ObjectId | null | undefined;
        description?: string | null | undefined;
        paidAt?: NativeDate | null | undefined;
        paymentTransaction?: mongoose.Types.ObjectId | null | undefined;
    } & mongoose.DefaultTimestampProps, {}, {
        timestamps: true;
    }> & {
        isActive: boolean;
        semester: string;
        student: mongoose.Types.ObjectId;
        dueDate: NativeDate;
        amount: number;
        academicYear: string;
        feeType: "tuition" | "registration" | "examination" | "library" | "hostel" | "other";
        isPaid: boolean;
        lateFee: number;
        totalAmount: number;
        course?: mongoose.Types.ObjectId | null | undefined;
        description?: string | null | undefined;
        paidAt?: NativeDate | null | undefined;
        paymentTransaction?: mongoose.Types.ObjectId | null | undefined;
    } & mongoose.DefaultTimestampProps & {
        _id: mongoose.Types.ObjectId;
    } & {
        __v: number;
    }, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
        timestamps: true;
    }, {
        isActive: boolean;
        semester: string;
        student: mongoose.Types.ObjectId;
        dueDate: NativeDate;
        amount: number;
        academicYear: string;
        feeType: "tuition" | "registration" | "examination" | "library" | "hostel" | "other";
        isPaid: boolean;
        lateFee: number;
        totalAmount: number;
        course?: mongoose.Types.ObjectId | null | undefined;
        description?: string | null | undefined;
        paidAt?: NativeDate | null | undefined;
        paymentTransaction?: mongoose.Types.ObjectId | null | undefined;
    } & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
        isActive: boolean;
        semester: string;
        student: mongoose.Types.ObjectId;
        dueDate: NativeDate;
        amount: number;
        academicYear: string;
        feeType: "tuition" | "registration" | "examination" | "library" | "hostel" | "other";
        isPaid: boolean;
        lateFee: number;
        totalAmount: number;
        course?: mongoose.Types.ObjectId | null | undefined;
        description?: string | null | undefined;
        paidAt?: NativeDate | null | undefined;
        paymentTransaction?: mongoose.Types.ObjectId | null | undefined;
    } & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
        timestamps: true;
    }>> & mongoose.FlatRecord<{
        isActive: boolean;
        semester: string;
        student: mongoose.Types.ObjectId;
        dueDate: NativeDate;
        amount: number;
        academicYear: string;
        feeType: "tuition" | "registration" | "examination" | "library" | "hostel" | "other";
        isPaid: boolean;
        lateFee: number;
        totalAmount: number;
        course?: mongoose.Types.ObjectId | null | undefined;
        description?: string | null | undefined;
        paidAt?: NativeDate | null | undefined;
        paymentTransaction?: mongoose.Types.ObjectId | null | undefined;
    } & mongoose.DefaultTimestampProps> & {
        _id: mongoose.Types.ObjectId;
    } & {
        __v: number;
    }>>;
};
export default _default;
//# sourceMappingURL=Payment.d.ts.map