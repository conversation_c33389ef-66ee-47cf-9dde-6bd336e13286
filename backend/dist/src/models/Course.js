import mongoose from 'mongoose';
const courseSchema = new mongoose.Schema({
    code: {
        type: String,
        required: true,
        trim: true,
        uppercase: true
    },
    title: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    credits: {
        type: Number,
        required: true,
        min: 1,
        max: 6
    },
    lecturer: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    department: {
        type: String,
        required: true,
        trim: true
    },
    capacity: {
        type: Number,
        required: true,
        min: 1,
        max: 200
    },
    enrolled: {
        type: Number,
        default: 0,
        min: 0
    },
    prerequisites: [{
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Course'
        }],
    semester: {
        type: String,
        required: true,
        trim: true
    },
    academicYear: {
        type: String,
        required: true,
        trim: true
    },
    schedule: [{
            day: {
                type: String,
                enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            },
            startTime: String,
            endTime: String,
            room: String,
            type: {
                type: String,
                enum: ['lecture', 'lab', 'tutorial', 'seminar']
            }
        }],
    isActive: {
        type: Boolean,
        default: true
    },
    startDate: {
        type: Date
    },
    endDate: {
        type: Date
    },
    syllabus: {
        type: String
    },
    objectives: [String],
    learningOutcomes: [String],
    assessmentMethods: [String],
    textbooks: [{
            title: String,
            author: String,
            edition: String,
            isbn: String,
            required: Boolean
        }],
    resources: [{
            title: String,
            type: String,
            url: String,
            description: String
        }]
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Indexes
courseSchema.index({ code: 1, semester: 1, academicYear: 1 }, { unique: true });
courseSchema.index({ lecturer: 1 });
courseSchema.index({ department: 1 });
courseSchema.index({ semester: 1 });
courseSchema.index({ academicYear: 1 });
courseSchema.index({ isActive: 1 });
// Virtual for availability
courseSchema.virtual('isAvailable').get(function () {
    return this.enrolled < this.capacity;
});
// Virtual for enrollment percentage
courseSchema.virtual('enrollmentPercentage').get(function () {
    return Math.round((this.enrolled / this.capacity) * 100);
});
// Pre-save middleware to validate dates
courseSchema.pre('save', function (next) {
    if (this.startDate && this.endDate && this.startDate >= this.endDate) {
        return next(new Error('Start date must be before end date'));
    }
    next();
});
// Method to check if student can enroll
courseSchema.methods.canEnroll = function (studentId) {
    // Check if course is active
    if (!this.isActive)
        return false;
    // Check if course has capacity
    if (this.enrolled >= this.capacity)
        return false;
    // Check if student meets prerequisites
    // This would need to be implemented based on enrollment records
    return true;
};
// Method to enroll student
courseSchema.methods.enrollStudent = function () {
    if (this.enrolled < this.capacity) {
        this.enrolled += 1;
        return this.save();
    }
    throw new Error('Course is full');
};
// Method to drop student
courseSchema.methods.dropStudent = function () {
    if (this.enrolled > 0) {
        this.enrolled -= 1;
        return this.save();
    }
    throw new Error('No students enrolled');
};
// Static method to find by department
courseSchema.statics.findByDepartment = function (department) {
    return this.find({ department: department, isActive: true });
};
// Static method to find by lecturer
courseSchema.statics.findByLecturer = function (lecturerId) {
    return this.find({ lecturer: lecturerId, isActive: true });
};
// Static method to find by semester
courseSchema.statics.findBySemester = function (semester, academicYear) {
    return this.find({ semester: semester, academicYear: academicYear, isActive: true });
};
// Static method to find available courses
courseSchema.statics.findAvailable = function () {
    return this.find({ isActive: true, enrolled: { $lt: '$capacity' } });
};
export const Course = mongoose.model('Course', courseSchema);
export default Course;
//# sourceMappingURL=Course.js.map