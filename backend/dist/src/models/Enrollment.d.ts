import mongoose from 'mongoose';
export declare const Enrollment: mongoose.Model<{
    semester: string;
    student: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    status: "completed" | "failed" | "enrolled" | "dropped";
    academicYear: string;
    enrolledAt: NativeDate;
    creditsEarned: number;
    completedAt?: NativeDate | null | undefined;
    notes?: string | null | undefined;
    finalGrade?: "D" | "A" | "B" | "C" | "F" | "P" | "NP" | null | undefined;
    droppedAt?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    semester: string;
    student: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    status: "completed" | "failed" | "enrolled" | "dropped";
    academicYear: string;
    enrolledAt: NativeDate;
    creditsEarned: number;
    completedAt?: NativeDate | null | undefined;
    notes?: string | null | undefined;
    finalGrade?: "D" | "A" | "B" | "C" | "F" | "P" | "NP" | null | undefined;
    droppedAt?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}> & {
    semester: string;
    student: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    status: "completed" | "failed" | "enrolled" | "dropped";
    academicYear: string;
    enrolledAt: NativeDate;
    creditsEarned: number;
    completedAt?: NativeDate | null | undefined;
    notes?: string | null | undefined;
    finalGrade?: "D" | "A" | "B" | "C" | "F" | "P" | "NP" | null | undefined;
    droppedAt?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}, {
    semester: string;
    student: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    status: "completed" | "failed" | "enrolled" | "dropped";
    academicYear: string;
    enrolledAt: NativeDate;
    creditsEarned: number;
    completedAt?: NativeDate | null | undefined;
    notes?: string | null | undefined;
    finalGrade?: "D" | "A" | "B" | "C" | "F" | "P" | "NP" | null | undefined;
    droppedAt?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    semester: string;
    student: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    status: "completed" | "failed" | "enrolled" | "dropped";
    academicYear: string;
    enrolledAt: NativeDate;
    creditsEarned: number;
    completedAt?: NativeDate | null | undefined;
    notes?: string | null | undefined;
    finalGrade?: "D" | "A" | "B" | "C" | "F" | "P" | "NP" | null | undefined;
    droppedAt?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}>> & mongoose.FlatRecord<{
    semester: string;
    student: mongoose.Types.ObjectId;
    course: mongoose.Types.ObjectId;
    status: "completed" | "failed" | "enrolled" | "dropped";
    academicYear: string;
    enrolledAt: NativeDate;
    creditsEarned: number;
    completedAt?: NativeDate | null | undefined;
    notes?: string | null | undefined;
    finalGrade?: "D" | "A" | "B" | "C" | "F" | "P" | "NP" | null | undefined;
    droppedAt?: NativeDate | null | undefined;
} & mongoose.DefaultTimestampProps> & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}>>;
export default Enrollment;
//# sourceMappingURL=Enrollment.d.ts.map