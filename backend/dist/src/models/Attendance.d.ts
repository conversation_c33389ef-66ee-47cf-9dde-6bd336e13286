import mongoose from 'mongoose';
export declare const Attendance: mongoose.Model<{
    session: mongoose.Types.ObjectId;
    student: mongoose.Types.ObjectId;
    date: NativeDate;
    status: "present" | "absent" | "late" | "excused";
    markedBy: mongoose.Types.ObjectId;
    markedAt: NativeDate;
    isExcused: boolean;
    notes?: string | null | undefined;
    excuseReason?: string | null | undefined;
    excuseDocument?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {}, {}, mongoose.Document<unknown, {}, {
    session: mongoose.Types.ObjectId;
    student: mongoose.Types.ObjectId;
    date: NativeDate;
    status: "present" | "absent" | "late" | "excused";
    markedBy: mongoose.Types.ObjectId;
    markedAt: NativeDate;
    isExcused: boolean;
    notes?: string | null | undefined;
    excuseReason?: string | null | undefined;
    excuseDocument?: string | null | undefined;
} & mongoose.DefaultTimestampProps, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}> & {
    session: mongoose.Types.ObjectId;
    student: mongoose.Types.ObjectId;
    date: NativeDate;
    status: "present" | "absent" | "late" | "excused";
    markedBy: mongoose.Types.ObjectId;
    markedAt: NativeDate;
    isExcused: boolean;
    notes?: string | null | undefined;
    excuseReason?: string | null | undefined;
    excuseDocument?: string | null | undefined;
} & mongoose.DefaultTimestampProps & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}, mongoose.Schema<any, mongoose.Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}, {
    session: mongoose.Types.ObjectId;
    student: mongoose.Types.ObjectId;
    date: NativeDate;
    status: "present" | "absent" | "late" | "excused";
    markedBy: mongoose.Types.ObjectId;
    markedAt: NativeDate;
    isExcused: boolean;
    notes?: string | null | undefined;
    excuseReason?: string | null | undefined;
    excuseDocument?: string | null | undefined;
} & mongoose.DefaultTimestampProps, mongoose.Document<unknown, {}, mongoose.FlatRecord<{
    session: mongoose.Types.ObjectId;
    student: mongoose.Types.ObjectId;
    date: NativeDate;
    status: "present" | "absent" | "late" | "excused";
    markedBy: mongoose.Types.ObjectId;
    markedAt: NativeDate;
    isExcused: boolean;
    notes?: string | null | undefined;
    excuseReason?: string | null | undefined;
    excuseDocument?: string | null | undefined;
} & mongoose.DefaultTimestampProps>, {}, mongoose.ResolveSchemaOptions<{
    timestamps: true;
    toJSON: {
        virtuals: true;
    };
    toObject: {
        virtuals: true;
    };
}>> & mongoose.FlatRecord<{
    session: mongoose.Types.ObjectId;
    student: mongoose.Types.ObjectId;
    date: NativeDate;
    status: "present" | "absent" | "late" | "excused";
    markedBy: mongoose.Types.ObjectId;
    markedAt: NativeDate;
    isExcused: boolean;
    notes?: string | null | undefined;
    excuseReason?: string | null | undefined;
    excuseDocument?: string | null | undefined;
} & mongoose.DefaultTimestampProps> & {
    _id: mongoose.Types.ObjectId;
} & {
    __v: number;
}>>;
export default Attendance;
//# sourceMappingURL=Attendance.d.ts.map