export declare function publishRealtimeUpdate(eventType: any, data: any): Promise<void>;
export declare const REALTIME_EVENTS: {
    USER_CREATED: string;
    USER_UPDATED: string;
    USER_DELETED: string;
    PROFILE_UPDATED: string;
    COURSE_CREATED: string;
    COURSE_UPDATED: string;
    COURSE_DELETED: string;
    COURSE_ENROLLED: string;
    COURSE_DROPPED: string;
    GRADE_CREATED: string;
    GRADE_UPDATED: string;
    GRADE_DELETED: string;
    ASSIGNMENT_CREATED: string;
    ASSIGNMENT_UPDATED: string;
    ASSIGNMENT_DELETED: string;
    ASSIGNMENT_SUBMITTED: string;
    ASSIGNMENT_GRADED: string;
    ATTENDANCE_MARKED: string;
    ATTENDANCE_UPDATED: string;
    SESSION_CREATED: string;
    SESSION_UPDATED: string;
    SESSION_STARTED: string;
    SESSION_ENDED: string;
    NOTIFICATION_CREATED: string;
    NOTIFICATION_READ: string;
    SYSTEM_MAINTENANCE: string;
    SYSTEM_ALERT: string;
};
export declare const ROLE_NAMESPACES: {
    student: string;
    lecturer: string;
    admin: string;
    finance: string;
    registrar: string;
};
export declare function getNamespaceForRoles(roles: any): any;
export declare function filterEventForUser(eventType: any, data: any, userRoles: any): boolean;
export declare function createFilteredMessage(eventType: any, data: any, userRoles: any, userId: any): {
    type: any;
    data: any;
    timestamp: string;
    id: string;
} | null;
export declare function checkUpdateRateLimit(userId: any, eventType: any): boolean;
//# sourceMappingURL=realtime.d.ts.map