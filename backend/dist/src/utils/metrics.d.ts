import client from 'prom-client';
declare const register: client.Registry<"text/plain; version=0.0.4; charset=utf-8">;
export declare const httpMetricsMiddleware: (req: any, res: any, next: any) => void;
export declare const updateConnectionMetrics: (type: any, count: any) => void;
export declare const updateCacheMetrics: (hitRate: any) => void;
export declare const recordRealtimeEvent: (eventType: any, namespace: any) => void;
export declare const updateUserSessionMetrics: (role: any, count: any) => void;
export { register };
export default register;
//# sourceMappingURL=metrics.d.ts.map