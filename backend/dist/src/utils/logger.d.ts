import winston from 'winston';
declare const logger: winston.Logger;
export declare const requestLogger: (req: any, res: any, next: any) => void;
export declare const errorLogger: (error: any, req: any, res: any, next: any) => void;
export declare const performanceLogger: (operation: any, duration: any, metadata?: {}) => void;
export declare const securityLogger: (event: any, details?: {}) => void;
export declare const auditLogger: (action: any, resource: any, userId: any, details?: {}) => void;
export declare const dbLogger: (operation: any, collection: any, duration: any, metadata?: {}) => void;
export declare const cacheLogger: (operation: any, key: any, hit: any, duration: any, metadata?: {}) => void;
export declare const rateLimitLogger: (ip: any, endpoint: any, limit: any, remaining: any) => void;
export declare const healthLogger: (service: any, status: any, details?: {}) => void;
export declare const logLevels: {
    ERROR: string;
    WARN: string;
    INFO: string;
    DEBUG: string;
};
export declare const logStructured: (level: any, message: any, metadata?: {}) => void;
export declare const logError: (message: any, error: any, metadata?: {}) => void;
export declare const logWarning: (message: any, metadata?: {}) => void;
export declare const logInfo: (message: any, metadata?: {}) => void;
export declare const logDebug: (message: any, metadata?: {}) => void;
export { logger };
export default logger;
//# sourceMappingURL=logger.d.ts.map