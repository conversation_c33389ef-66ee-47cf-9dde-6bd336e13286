import client from 'prom-client';
// Create a Registry to register the metrics
const register = new client.Registry();
// Add a default label which is added to all metrics
register.setDefaultLabels({
    app: 'university-portal-api',
    version: process.env.npm_package_version || '1.0.0'
});
// Enable the collection of default metrics
client.collectDefaultMetrics({ register });
// Custom metrics
const httpRequestDuration = new client.Histogram({
    name: 'http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames: ['method', 'route', 'status_code'],
    buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
});
const httpRequestTotal = new client.Counter({
    name: 'http_requests_total',
    help: 'Total number of HTTP requests',
    labelNames: ['method', 'route', 'status_code']
});
const activeConnections = new client.Gauge({
    name: 'active_connections',
    help: 'Number of active connections'
});
const databaseConnections = new client.Gauge({
    name: 'database_connections',
    help: 'Number of database connections'
});
const redisConnections = new client.Gauge({
    name: 'redis_connections',
    help: 'Number of Redis connections'
});
const cacheHitRate = new client.Gauge({
    name: 'cache_hit_rate',
    help: 'Cache hit rate percentage'
});
const realtimeEvents = new client.Counter({
    name: 'realtime_events_total',
    help: 'Total number of real-time events',
    labelNames: ['event_type', 'namespace']
});
const userSessions = new client.Gauge({
    name: 'user_sessions_active',
    help: 'Number of active user sessions',
    labelNames: ['role']
});
// Register custom metrics
register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestTotal);
register.registerMetric(activeConnections);
register.registerMetric(databaseConnections);
register.registerMetric(redisConnections);
register.registerMetric(cacheHitRate);
register.registerMetric(realtimeEvents);
register.registerMetric(userSessions);
// Middleware to collect HTTP metrics
export const httpMetricsMiddleware = (req, res, next) => {
    const start = Date.now();
    res.on('finish', () => {
        const duration = (Date.now() - start) / 1000;
        const route = req.route?.path || req.path;
        httpRequestDuration
            .labels(req.method, route, res.statusCode)
            .observe(duration);
        httpRequestTotal
            .labels(req.method, route, res.statusCode)
            .inc();
    });
    next();
};
// Function to update connection metrics
export const updateConnectionMetrics = (type, count) => {
    switch (type) {
        case 'database':
            databaseConnections.set(count);
            break;
        case 'redis':
            redisConnections.set(count);
            break;
        case 'active':
            activeConnections.set(count);
            break;
    }
};
// Function to update cache metrics
export const updateCacheMetrics = (hitRate) => {
    cacheHitRate.set(hitRate);
};
// Function to record real-time events
export const recordRealtimeEvent = (eventType, namespace) => {
    realtimeEvents.labels(eventType, namespace).inc();
};
// Function to update user session metrics
export const updateUserSessionMetrics = (role, count) => {
    userSessions.labels(role).set(count);
};
export { register };
export default register;
//# sourceMappingURL=metrics.js.map