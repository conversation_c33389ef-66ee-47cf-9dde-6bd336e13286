export declare function sendEmail({ to, subject, template, data, html, text }: {
    to: any;
    subject: any;
    template: any;
    data: any;
    html: any;
    text: any;
}): Promise<any>;
export declare function sendBulkEmails(emails: any): Promise<{
    results: any[];
    errors: {
        email: any;
        error: any;
    }[];
}>;
declare const _default: {
    sendEmail: typeof sendEmail;
    sendBulkEmails: typeof sendBulkEmails;
};
export default _default;
//# sourceMappingURL=email.d.ts.map