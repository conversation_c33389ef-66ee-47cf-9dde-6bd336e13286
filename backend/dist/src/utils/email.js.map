{"version": 3, "file": "email.js", "sourceRoot": "", "sources": ["../../../src/utils/email.ts"], "names": [], "mappings": "AAAA,OAAO,UAAU,MAAM,YAAY,CAAC;AACpC,OAAO,MAAM,MAAM,oBAAoB,CAAC;AACxC,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAErC,qBAAqB;AACrB,MAAM,WAAW,GAAG,UAAU,CAAC,iBAAiB,CAAC;IAC/C,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;IACvB,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;IACvB,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM;IAC3B,IAAI,EAAE;QACJ,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;QACvB,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ;KAC5B;CACF,CAAC,CAAC;AAEH,mCAAmC;AACnC,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;IACpC,IAAI,KAAK,EAAE,CAAC;QACV,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;IAChE,CAAC;SAAM,CAAC;QACN,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;IAC7D,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kBAAkB;AAClB,MAAM,SAAS,GAAG;IAChB,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAClB,OAAO,EAAE,8BAA8B;QACvC,IAAI,EAAE;;;mBAGS,IAAI,CAAC,IAAI;;;;;KAKvB;QACD,IAAI,EAAE,0CAA0C,IAAI,CAAC,IAAI,2NAA2N;KACrR,CAAC;IAEF,aAAa,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACxB,OAAO,EAAE,wBAAwB;QACjC,IAAI,EAAE;;;mBAGS,IAAI,CAAC,IAAI;;sBAEN,IAAI,CAAC,SAAS;;;;;KAK/B;QACD,IAAI,EAAE,mCAAmC,IAAI,CAAC,IAAI,uGAAuG,IAAI,CAAC,SAAS,wJAAwJ;KAChU,CAAC;IAEF,iBAAiB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAC5B,OAAO,EAAE,kBAAkB;QAC3B,IAAI,EAAE;;;mBAGS,IAAI,CAAC,WAAW;kEAC+B,IAAI,CAAC,UAAU;yBACxD,IAAI,CAAC,eAAe;4BACjB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ;;;;KAIlD;QACD,IAAI,EAAE,6BAA6B,IAAI,CAAC,WAAW,qDAAqD,IAAI,CAAC,UAAU,mBAAmB,IAAI,CAAC,eAAe,YAAY,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,gGAAgG;KACtS,CAAC;IAEF,aAAa,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACxB,OAAO,EAAE,qBAAqB;QAC9B,IAAI,EAAE;;;mBAGS,IAAI,CAAC,WAAW;;sCAEG,IAAI,CAAC,UAAU;0CACX,IAAI,CAAC,eAAe;wCACtB,IAAI,CAAC,OAAO;;;;KAI/C;QACD,IAAI,EAAE,gCAAgC,IAAI,CAAC,WAAW,wEAAwE,IAAI,CAAC,UAAU,iBAAiB,IAAI,CAAC,eAAe,eAAe,IAAI,CAAC,OAAO,8GAA8G;KAC5T,CAAC;CACH,CAAC;AAEF,sBAAsB;AACtB,MAAM,CAAC,KAAK,UAAU,SAAS,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;IACzE,IAAI,CAAC;QACH,IAAI,YAAY,CAAC;QAEjB,IAAI,QAAQ,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpC,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC;aAAM,CAAC;YACN,YAAY,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACzC,CAAC;QAED,MAAM,WAAW,GAAG;YAClB,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;YACvB,EAAE;YACF,OAAO,EAAE,YAAY,CAAC,OAAO;YAC7B,IAAI,EAAE,YAAY,CAAC,IAAI;YACvB,IAAI,EAAE,YAAY,CAAC,IAAI;SACxB,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QACvD,MAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;QAEtF,OAAO,MAAM,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC5C,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,mBAAmB;AACnB,MAAM,CAAC,KAAK,UAAU,cAAc,CAAC,MAAM;IACzC,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,MAAM,MAAM,GAAG,EAAE,CAAC;IAElB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QAC3B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,KAAK,CAAC,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AAC7B,CAAC;AAED,eAAe,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC"}