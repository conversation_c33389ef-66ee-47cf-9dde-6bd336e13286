declare const config: {
    server: {
        port: number;
        host: string;
        env: string;
        trustProxy: boolean;
    };
    database: {
        uri: string;
        options: any;
    };
    redis: {
        url: string;
        cluster: {
            nodes: string[];
            options: {
                password: string | undefined;
                retryDelayOnFailover: number;
                enableReadyCheck: boolean;
                maxRetriesPerRequest: null;
            };
        };
    };
    jwt: {
        secret: string;
        refreshSecret: string;
        accessExpiresIn: string;
        refreshExpiresIn: string;
        issuer: string;
        audience: string;
        algorithm: string;
    };
    security: {
        bcryptRounds: number;
        argon2: {
            memoryLimit: number;
            timeCost: number;
            parallelism: number;
        };
    };
    rateLimit: {
        windowMs: number;
        maxRequests: number;
        skipSuccessfulRequests: boolean;
    };
    upload: {
        maxFileSize: number;
        allowedTypes: string[];
        uploadPath: string;
        s3: {
            bucket: string;
            region: string;
            accessKeyId: string | undefined;
            secretAccessKey: string | undefined;
        };
    };
    email: {
        smtp: {
            host: string;
            port: number;
            secure: boolean;
            auth: {
                user: string | undefined;
                pass: string | undefined;
            };
        };
        from: {
            email: string;
            name: string;
        };
    };
    mfa: {
        issuer: string;
        algorithm: string;
        digits: number;
        period: number;
    };
    monitoring: {
        prometheus: {
            port: number;
        };
        jaeger: {
            endpoint: string;
        };
        logging: {
            level: string;
            format: string;
        };
    };
    socket: {
        rateLimit: {
            max: number;
            window: number;
        };
    };
    cache: {
        ttl: {
            courses: number;
            grades: number;
            users: number;
            permissions: number;
        };
    };
    kubernetes: {
        namespace: string;
        serviceName: string;
        replicas: number;
        maxReplicas: number;
        cpuTarget: number;
        resources: {
            memory: string;
            cpu: string;
        };
    };
    healthCheck: {
        interval: number;
        timeout: number;
    };
    features: {
        mfa: boolean;
        fileUpload: boolean;
        realTime: boolean;
        caching: boolean;
        metrics: boolean;
        tracing: boolean;
    };
    development: {
        seedDatabase: boolean;
        hotReload: boolean;
        debugMode: boolean;
    };
};
export default config;
//# sourceMappingURL=index.d.ts.map