// Payment service implementations for Kenyan payment methods
import axios from 'axios';
import * as crypto from 'crypto';
import { PaymentTransaction, PaymentMethod, Fee, PaymentWebhook } from '../models/Payment.js';
import { logger } from '../utils/logger.js';

// Define AxiosInstance type locally since it's not exported from axios
type AxiosInstance = ReturnType<typeof axios.create>;
import { publishRealtimeUpdate } from '../utils/realtime.js';

// Payment configuration interface
export interface PaymentConfig {
  mpesa: {
    consumerKey: string;
    consumerSecret: string;
    businessShortCode: string;
    passkey: string;
    environment: 'sandbox' | 'production';
    callbackUrl: string;
  };
  jambopay: {
    apiKey: string;
    merchantId: string;
    environment: 'sandbox' | 'production';
    callbackUrl: string;
  };
  imBank: {
    clientId: string;
    clientSecret: string;
    baseUrl: string;
    environment: 'sandbox' | 'production';
  };
  kcbBuni: {
    apiKey: string;
    merchantId: string;
    baseUrl: string;
    environment: 'sandbox' | 'production';
  };
  pesalink: {
    apiKey: string;
    merchantId: string;
    baseUrl: string;
    environment: 'sandbox' | 'production';
  };
}

// Payment request interface
export interface PaymentRequest {
  studentId: string;
  feeId: string;
  amount: number;
  phoneNumber?: string;
  accountNumber?: string;
  bankCode?: string;
  description?: string;
}

// Payment response interface
export interface PaymentResponse {
  success: boolean;
  transactionId?: string;
  referenceNumber?: string;
  message: string;
  providerResponse?: any;
  error?: string;
}

// M-Pesa Service Implementation
export class MpesaService {
  private config: PaymentConfig['mpesa'];
  private httpClient: AxiosInstance;

  constructor(config: PaymentConfig['mpesa']) {
    this.config = config;
    this.httpClient = axios.create({
      baseURL: config.environment === 'production' 
        ? 'https://api.safaricom.co.ke' 
        : 'https://sandbox.safaricom.co.ke',
      timeout: 30000,
    });
  }

  async initiatePayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      logger.info('Initiating M-Pesa payment', { 
        studentId: request.studentId, 
        amount: request.amount,
        phoneNumber: request.phoneNumber 
      });

      // Validate phone number
      if (!request.phoneNumber || !this.isValidPhoneNumber(request.phoneNumber)) {
        throw new Error('Invalid phone number format');
      }

      // Get access token
      const accessToken = await this.getAccessToken();
      
      // Generate timestamp and password
      const timestamp = this.generateTimestamp();
      const password = this.generatePassword(timestamp);
      
      // Generate reference number
      const referenceNumber = this.generateReferenceNumber(request.studentId);
      
      // Prepare STK Push request
      const stkPushRequest = {
        BusinessShortCode: this.config.businessShortCode,
        Password: password,
        Timestamp: timestamp,
        TransactionType: 'CustomerPayBillOnline',
        Amount: Math.round(request.amount),
        PartyA: request.phoneNumber,
        PartyB: this.config.businessShortCode,
        PhoneNumber: request.phoneNumber,
        CallBackURL: this.config.callbackUrl,
        AccountReference: referenceNumber,
        TransactionDesc: request.description || 'University fee payment',
      };

      logger.debug('M-Pesa STK Push request', { stkPushRequest });

      // Make API call
      const response = await this.httpClient.post('/mpesa/stkpush/v1/processrequest', stkPushRequest, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      logger.info('M-Pesa payment initiated successfully', {
        checkoutRequestId: response.data.CheckoutRequestID,
        merchantRequestId: response.data.MerchantRequestID
      });

      return {
        success: true,
        transactionId: response.data.CheckoutRequestID,
        referenceNumber: referenceNumber,
        message: 'Payment initiated successfully. Please complete the payment on your phone.',
        providerResponse: response.data
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      logger.error('M-Pesa payment initiation failed', { error: errorMessage });
      return {
        success: false,
        message: 'Payment initiation failed',
        error: errorMessage
      };
    }
  }

  async verifyPayment(checkoutRequestId: string): Promise<any> {
    try {
      const accessToken = await this.getAccessToken();
      
      const response = await this.httpClient.post('/mpesa/stkpushquery/v1/query', {
        BusinessShortCode: this.config.businessShortCode,
        Password: this.generatePassword(this.generateTimestamp()),
        Timestamp: this.generateTimestamp(),
        CheckoutRequestID: checkoutRequestId
      }, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      return response.data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      logger.error('M-Pesa payment verification failed', { error: errorMessage });
      throw error;
    }
  }

  private async getAccessToken(): Promise<string> {
    try {
      const auth = Buffer.from(`${this.config.consumerKey}:${this.config.consumerSecret}`).toString('base64');
      
      const response = await this.httpClient.get('/oauth/v1/generate?grant_type=client_credentials', {
        headers: {
          'Authorization': `Basic ${auth}`,
        },
      });

      return response.data.access_token;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      logger.error('Failed to get M-Pesa access token', { error: errorMessage });
      throw error;
    }
  }

  private generateTimestamp(): string {
    return new Date().toISOString().replace(/[^0-9]/g, '').slice(0, -3);
  }

  private generatePassword(timestamp: string): string {
    const data = `${this.config.businessShortCode}${this.config.passkey}${timestamp}`;
    return Buffer.from(data).toString('base64');
  }

  private generateReferenceNumber(studentId: string): string {
    const timestamp = Date.now().toString().slice(-6);
    return `UNI${studentId.slice(-4)}${timestamp}`;
  }

  private isValidPhoneNumber(phoneNumber: string): boolean {
    // Remove any non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    // Check if it's a valid Kenyan phone number
    return /^254[17]\d{8}$/.test(cleaned);
  }
}

// JamboPay Service Implementation
export class JamboPayService {
  private config: PaymentConfig['jambopay'];
  private httpClient: AxiosInstance;

  constructor(config: PaymentConfig['jambopay']) {
    this.config = config;
    this.httpClient = axios.create({
      baseURL: config.environment === 'production' 
        ? 'https://api.jambopay.com' 
        : 'https://sandbox.jambopay.com',
      timeout: 30000,
    });
  }

  async initiatePayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      logger.info('Initiating JamboPay payment', { 
        studentId: request.studentId, 
        amount: request.amount 
      });

      const referenceNumber = this.generateReferenceNumber(request.studentId);
      
      const paymentRequest = {
        merchantId: this.config.merchantId,
        amount: request.amount,
        currency: 'KES',
        reference: referenceNumber,
        description: request.description || 'University fee payment',
        callbackUrl: this.config.callbackUrl,
        phoneNumber: request.phoneNumber,
        metadata: {
          studentId: request.studentId,
          feeId: request.feeId
        }
      };

      const response = await this.httpClient.post('/api/v1/payments', paymentRequest, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      logger.info('JamboPay payment initiated successfully', {
        transactionId: response.data.transactionId
      });

      return {
        success: true,
        transactionId: response.data.transactionId,
        referenceNumber: referenceNumber,
        message: 'Payment initiated successfully',
        providerResponse: response.data
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      logger.error('JamboPay payment initiation failed', { error: errorMessage });
      return {
        success: false,
        message: 'Payment initiation failed',
        error: errorMessage
      };
    }
  }

  private generateReferenceNumber(studentId: string): string {
    const timestamp = Date.now().toString().slice(-6);
    return `JP${studentId.slice(-4)}${timestamp}`;
  }
}

// I&M Bank Service Implementation
export class ImBankService {
  private config: PaymentConfig['imBank'];
  private httpClient: AxiosInstance;

  constructor(config: PaymentConfig['imBank']) {
    this.config = config;
    this.httpClient = axios.create({
      baseURL: config.baseUrl,
      timeout: 30000,
    });
  }

  async initiatePayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      logger.info('Initiating I&M Bank payment', { 
        studentId: request.studentId, 
        amount: request.amount 
      });

      const accessToken = await this.getAccessToken();
      const referenceNumber = this.generateReferenceNumber(request.studentId);
      
      const transferRequest = {
        accountNumber: request.accountNumber,
        amount: request.amount,
        referenceNumber: referenceNumber,
        description: request.description || 'University fee payment',
        currency: 'KES',
        timestamp: new Date().toISOString(),
        metadata: {
          studentId: request.studentId,
          feeId: request.feeId
        }
      };

      const response = await this.httpClient.post('/api/v1/transfers', transferRequest, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      logger.info('I&M Bank payment initiated successfully', {
        transactionId: response.data.transactionId
      });

      return {
        success: true,
        transactionId: response.data.transactionId,
        referenceNumber: referenceNumber,
        message: 'Bank transfer initiated successfully',
        providerResponse: response.data
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      logger.error('I&M Bank payment initiation failed', { error: errorMessage });
      return {
        success: false,
        message: 'Bank transfer initiation failed',
        error: errorMessage
      };
    }
  }

  private async getAccessToken(): Promise<string> {
    try {
      const response = await this.httpClient.post('/oauth/token', {
        grant_type: 'client_credentials',
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
      });

      return response.data.access_token;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      logger.error('Failed to get I&M Bank access token', { error: errorMessage });
      throw error;
    }
  }

  private generateReferenceNumber(studentId: string): string {
    const timestamp = Date.now().toString().slice(-6);
    return `IM${studentId.slice(-4)}${timestamp}`;
  }
}

// Payment Service Manager
export class PaymentServiceManager {
  private mpesaService: MpesaService;
  private jambopayService: JamboPayService;
  private imBankService: ImBankService;
  private config: PaymentConfig;

  constructor(config: PaymentConfig) {
    this.config = config;
    this.mpesaService = new MpesaService(config.mpesa);
    this.jambopayService = new JamboPayService(config.jambopay);
    this.imBankService = new ImBankService(config.imBank);
  }

  async initiatePayment(paymentMethod: string, request: PaymentRequest): Promise<PaymentResponse> {
    try {
      // Create payment transaction record
      const paymentMethodDoc = await PaymentMethod.findOne({ code: paymentMethod });
      if (!paymentMethodDoc) {
        throw new Error('Invalid payment method');
      }

      const transaction = new PaymentTransaction({
        student: request.studentId,
        fee: request.feeId,
        amount: request.amount,
        paymentMethod: paymentMethodDoc._id,
        referenceNumber: this.generateReferenceNumber(request.studentId),
        metadata: {
          phoneNumber: request.phoneNumber,
          accountNumber: request.accountNumber,
          bankCode: request.bankCode
        },
        netAmount: request.amount
      });

      await transaction.save();

      // Initiate payment based on method
      let paymentResponse: PaymentResponse;

      switch (paymentMethod) {
        case 'mpesa':
          paymentResponse = await this.mpesaService.initiatePayment(request);
          break;
        case 'jambopay':
          paymentResponse = await this.jambopayService.initiatePayment(request);
          break;
        case 'im_bank':
          paymentResponse = await this.imBankService.initiatePayment(request);
          break;
        default:
          throw new Error(`Unsupported payment method: ${paymentMethod}`);
      }

      // Update transaction with provider response
      if (paymentResponse.success) {
        transaction.providerTransactionId = paymentResponse.transactionId;
        transaction.status = 'initiated';
        if (paymentResponse.referenceNumber) {
          transaction.referenceNumber = paymentResponse.referenceNumber;
        }
        if (transaction.metadata) {
          transaction.metadata.providerResponse = paymentResponse.providerResponse;
        }
      } else {
        transaction.status = 'failed';
        transaction.failureReason = paymentResponse.error;
        transaction.failedAt = new Date();
      }

      await transaction.save();

      // Publish real-time update
      await publishRealtimeUpdate('payment_initiated', {
        transaction: {
          id: transaction._id,
          studentId: request.studentId,
          amount: request.amount,
          paymentMethod: paymentMethod,
          status: transaction.status
        },
        initiatedBy: request.studentId
      });

      return {
        ...paymentResponse,
        referenceNumber: transaction.referenceNumber
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      logger.error('Payment initiation failed', { error: errorMessage });
      return {
        success: false,
        message: 'Payment initiation failed',
        error: errorMessage
      };
    }
  }

  async handleWebhook(provider: string, payload: any, signature: string): Promise<void> {
    try {
      logger.info('Processing webhook', { provider, eventType: payload.eventType });

      // Verify webhook signature
      if (!this.verifyWebhookSignature(provider, payload, signature)) {
        throw new Error('Invalid webhook signature');
      }

      // Create webhook record
      const webhook = new PaymentWebhook({
        provider,
        eventType: payload.eventType,
        payload,
        signature
      });

      await webhook.save();

      // Process webhook based on provider and event type
      await this.processWebhook(webhook);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      logger.error('Webhook processing failed', { error: errorMessage });
      throw error;
    }
  }

  private async processWebhook(webhook: any): Promise<void> {
    try {
      const { payload } = webhook;
      
      // Find transaction by provider transaction ID
      const transaction = await PaymentTransaction.findOne({
        providerTransactionId: payload.transactionId || payload.CheckoutRequestID
      });

      if (!transaction) {
        logger.warn('Transaction not found for webhook', { 
          providerTransactionId: payload.transactionId || payload.CheckoutRequestID 
        });
        return;
      }

      // Process based on provider
      switch (webhook.provider) {
        case 'mpesa':
          await this.processMpesaWebhook(transaction, payload);
          break;
        case 'jambopay':
          await this.processJamboPayWebhook(transaction, payload);
          break;
        case 'im_bank':
          await this.processImBankWebhook(transaction, payload);
          break;
      }

      // Mark webhook as processed
      webhook.processed = true;
      webhook.processedAt = new Date();
      await webhook.save();

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      logger.error('Webhook processing failed', { error: errorMessage });

      // Increment processing attempts
      webhook.processingAttempts += 1;
      await webhook.save();
      
      throw error;
    }
  }

  private async processMpesaWebhook(transaction: any, payload: any): Promise<void> {
    const { Body } = payload;
    const { stkCallback } = Body;
    
    if (stkCallback.ResultCode === 0) {
      // Payment successful
      transaction.status = 'completed';
      transaction.completedAt = new Date();
      transaction.metadata.callbackData = stkCallback;
      
      // Update fee payment status
      const fee = await Fee.findById(transaction.fee);
      if (fee) {
        fee.isPaid = true;
        fee.paidAt = new Date();
        fee.paymentTransaction = transaction._id;
        await fee.save();
      }

      // Send success notification
      await publishRealtimeUpdate('payment_completed', {
        transaction: {
          id: transaction._id,
          studentId: transaction.student,
          amount: transaction.amount,
          status: 'completed'
        },
        completedBy: transaction.student
      });

    } else {
      // Payment failed
      transaction.status = 'failed';
      transaction.failedAt = new Date();
      transaction.failureReason = stkCallback.ResultDesc;
      transaction.metadata.callbackData = stkCallback;

      // Send failure notification
      await publishRealtimeUpdate('payment_failed', {
        transaction: {
          id: transaction._id,
          studentId: transaction.student,
          amount: transaction.amount,
          status: 'failed',
          reason: stkCallback.ResultDesc
        },
        failedBy: transaction.student
      });
    }

    await transaction.save();
  }

  private async processJamboPayWebhook(transaction: any, payload: any): Promise<void> {
    if (payload.status === 'completed') {
      transaction.status = 'completed';
      transaction.completedAt = new Date();
      transaction.metadata.callbackData = payload;
      
      // Update fee payment status
      const fee = await Fee.findById(transaction.fee);
      if (fee) {
        fee.isPaid = true;
        fee.paidAt = new Date();
        fee.paymentTransaction = transaction._id;
        await fee.save();
      }
    } else if (payload.status === 'failed') {
      transaction.status = 'failed';
      transaction.failedAt = new Date();
      transaction.failureReason = payload.reason;
      transaction.metadata.callbackData = payload;
    }

    await transaction.save();
  }

  private async processImBankWebhook(transaction: any, payload: any): Promise<void> {
    if (payload.status === 'success') {
      transaction.status = 'completed';
      transaction.completedAt = new Date();
      transaction.metadata.callbackData = payload;
      
      // Update fee payment status
      const fee = await Fee.findById(transaction.fee);
      if (fee) {
        fee.isPaid = true;
        fee.paidAt = new Date();
        fee.paymentTransaction = transaction._id;
        await fee.save();
      }
    } else if (payload.status === 'failed') {
      transaction.status = 'failed';
      transaction.failedAt = new Date();
      transaction.failureReason = payload.error;
      transaction.metadata.callbackData = payload;
    }

    await transaction.save();
  }

  private verifyWebhookSignature(provider: string, payload: any, signature: string): boolean {
    // Implement signature verification based on provider
    switch (provider) {
      case 'mpesa':
        return this.verifyMpesaSignature(payload, signature);
      case 'jambopay':
        return this.verifyJamboPaySignature(payload, signature);
      case 'im_bank':
        return this.verifyImBankSignature(payload, signature);
      default:
        return false;
    }
  }

  private verifyMpesaSignature(payload: any, signature: string): boolean {
    const expectedSignature = crypto
      .createHmac('sha256', process.env.MPESA_WEBHOOK_SECRET!)
      .update(JSON.stringify(payload))
      .digest('hex');
    
    return signature === expectedSignature;
  }

  private verifyJamboPaySignature(payload: any, signature: string): boolean {
    const expectedSignature = crypto
      .createHmac('sha256', this.config.jambopay.apiKey)
      .update(JSON.stringify(payload))
      .digest('hex');
    
    return signature === expectedSignature;
  }

  private verifyImBankSignature(payload: any, signature: string): boolean {
    const expectedSignature = crypto
      .createHmac('sha256', this.config.imBank.clientSecret)
      .update(JSON.stringify(payload))
      .digest('hex');
    
    return signature === expectedSignature;
  }

  private generateReferenceNumber(studentId: string): string {
    const timestamp = Date.now().toString().slice(-6);
    return `UNI${studentId.slice(-4)}${timestamp}`;
  }
}

export default PaymentServiceManager;
