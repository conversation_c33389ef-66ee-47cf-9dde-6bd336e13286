import { Redis, Cluster } from 'ioredis';
import config from '../config/index.js';
import { logger } from '../utils/logger.js';

class RedisService {
  private client: Redis | null = null;
  private cluster: Cluster | null = null;
  private isConnected: boolean = false;

  constructor() {
    // Properties are initialized above
  }

  async connect() {
    try {
      if (config.redis.cluster.nodes.length > 1) {
        // Use Redis Cluster for production
        this.cluster = new Cluster(config.redis.cluster.nodes, {
          ...config.redis.cluster.options,
          enableReadyCheck: true,
          redisOptions: {
            password: config.redis.cluster.options.password,
          },
        });

        this.cluster.on('connect', () => {
          logger.info('Redis Cluster connected');
          this.isConnected = true;
        });

        this.cluster.on('error', (error) => {
          logger.error('Redis Cluster error:', error);
          this.isConnected = false;
        });

        this.cluster.on('close', () => {
          logger.warn('Redis Cluster connection closed');
          this.isConnected = false;
        });

        await this.cluster.ping();
        logger.info('Redis Cluster ready');
      } else {
        // Use single Redis instance for development
        this.client = new Redis(config.redis.url, {
          enableReadyCheck: false,
          maxRetriesPerRequest: null,
        });

        this.client.on('connect', () => {
          logger.info('Redis connected');
          this.isConnected = true;
        });

        this.client.on('error', (error: any) => {
          logger.error('Redis error:', error);
          this.isConnected = false;
        });

        this.client.on('close', () => {
          logger.warn('Redis connection closed');
          this.isConnected = false;
        });

        await this.client.ping();
        logger.info('Redis ready');
      }
    } catch (error) {
      logger.error('Failed to connect to Redis:', error);
      throw error;
    }
  }

  async disconnect() {
    try {
      if (this.cluster) {
        await this.cluster.disconnect();
        this.cluster = null;
      }
      if (this.client) {
        await this.client.disconnect();
        this.client = null;
      }
      this.isConnected = false;
      logger.info('Redis disconnected');
    } catch (error) {
      logger.error('Error disconnecting Redis:', error);
    }
  }

  getClient(): Redis | Cluster | null {
    return this.cluster || this.client;
  }

  // Cache Operations
  async get(key: string): Promise<any> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      const value = await client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error(`Redis GET error for key ${key}:`, error);
      return null;
    }
  }

  async set(key: string, value: any, ttl: number | null = null): Promise<boolean> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      const serializedValue = JSON.stringify(value);

      if (ttl) {
        await client.setex(key, ttl, serializedValue);
      } else {
        await client.set(key, serializedValue);
      }
      return true;
    } catch (error) {
      logger.error(`Redis SET error for key ${key}:`, error);
      return false;
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      await client.del(key);
      return true;
    } catch (error) {
      logger.error(`Redis DEL error for key ${key}:`, error);
      return false;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      const result = await client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error(`Redis EXISTS error for key ${key}:`, error);
      return false;
    }
  }

  async expire(key: string, ttl: number): Promise<boolean> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      await client.expire(key, ttl);
      return true;
    } catch (error) {
      logger.error(`Redis EXPIRE error for key ${key}:`, error);
      return false;
    }
  }

  async ttl(key: string): Promise<number> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      return await client.ttl(key);
    } catch (error) {
      logger.error(`Redis TTL error for key ${key}:`, error);
      return -1;
    }
  }

  // Hash Operations
  async hget(key: string, field: string): Promise<any> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      const value = await client.hget(key, field);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error(`Redis HGET error for key ${key}, field ${field}:`, error);
      return null;
    }
  }

  async hset(key: string, field: string, value: any): Promise<boolean> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      await client.hset(key, field, JSON.stringify(value));
      return true;
    } catch (error) {
      logger.error(`Redis HSET error for key ${key}, field ${field}:`, error);
      return false;
    }
  }

  async hdel(key: string, field: string): Promise<boolean> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      await client.hdel(key, field);
      return true;
    } catch (error) {
      logger.error(`Redis HDEL error for key ${key}, field ${field}:`, error);
      return false;
    }
  }

  async hgetall(key: string): Promise<Record<string, any>> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      const hash = await client.hgetall(key);
      const result: Record<string, any> = {};
      for (const [field, value] of Object.entries(hash)) {
        result[field] = JSON.parse(value as string);
      }
      return result;
    } catch (error) {
      logger.error(`Redis HGETALL error for key ${key}:`, error);
      return {};
    }
  }

  // List Operations
  async lpush(key: string, ...values: any[]): Promise<number> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      const serializedValues = values.map(v => JSON.stringify(v));
      return await client.lpush(key, ...serializedValues);
    } catch (error) {
      logger.error(`Redis LPUSH error for key ${key}:`, error);
      return 0;
    }
  }

  async rpop(key: string): Promise<any> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      const value = await client.rpop(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error(`Redis RPOP error for key ${key}:`, error);
      return null;
    }
  }

  async llen(key: string): Promise<number> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      return await client.llen(key);
    } catch (error) {
      logger.error(`Redis LLEN error for key ${key}:`, error);
      return 0;
    }
  }

  // Set Operations
  async sadd(key: string, ...members: string[]): Promise<number> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      return await client.sadd(key, ...members);
    } catch (error) {
      logger.error(`Redis SADD error for key ${key}:`, error);
      return 0;
    }
  }

  async srem(key: string, ...members: string[]): Promise<number> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      return await client.srem(key, ...members);
    } catch (error) {
      logger.error(`Redis SREM error for key ${key}:`, error);
      return 0;
    }
  }

  async smembers(key: string): Promise<string[]> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      return await client.smembers(key);
    } catch (error) {
      logger.error(`Redis SMEMBERS error for key ${key}:`, error);
      return [];
    }
  }

  async sismember(key: string, member: string): Promise<boolean> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      const result = await client.sismember(key, member);
      return result === 1;
    } catch (error) {
      logger.error(`Redis SISMEMBER error for key ${key}, member ${member}:`, error);
      return false;
    }
  }

  // Sorted Set Operations
  async zadd(key: string, score: number, member: string): Promise<number> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      return await client.zadd(key, score, member);
    } catch (error) {
      logger.error(`Redis ZADD error for key ${key}:`, error);
      return 0;
    }
  }

  async zrange(key: string, start: number, stop: number, withScores: boolean = false): Promise<string[]> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      if (withScores) {
        return await client.zrange(key, start, stop, 'WITHSCORES');
      } else {
        return await client.zrange(key, start, stop);
      }
    } catch (error) {
      logger.error(`Redis ZRANGE error for key ${key}:`, error);
      return [];
    }
  }

  async zrem(key: string, ...members: string[]): Promise<number> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      return await client.zrem(key, ...members);
    } catch (error) {
      logger.error(`Redis ZREM error for key ${key}:`, error);
      return 0;
    }
  }

  // Pub/Sub Operations
  async publish(channel: string, message: any): Promise<number> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      return await client.publish(channel, JSON.stringify(message));
    } catch (error) {
      logger.error(`Redis PUBLISH error for channel ${channel}:`, error);
      return 0;
    }
  }

  async subscribe(channel: string, callback: (message: any) => void): Promise<any> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      const subscriber = client.duplicate();

      await subscriber.subscribe(channel);

      subscriber.on('message', (ch: string, message: string) => {
        if (ch === channel) {
          try {
            const parsedMessage = JSON.parse(message);
            callback(parsedMessage);
          } catch (error) {
            logger.error('Error parsing Redis message:', error);
          }
        }
      });

      return subscriber;
    } catch (error) {
      logger.error(`Redis SUBSCRIBE error for channel ${channel}:`, error);
      return null;
    }
  }

  // Cache Invalidation Patterns
  async invalidatePattern(pattern: string): Promise<number> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      const keys = await client.keys(pattern);

      if (keys.length > 0) {
        await client.del(...keys);
        logger.info(`Invalidated ${keys.length} keys matching pattern: ${pattern}`);
      }

      return keys.length;
    } catch (error) {
      logger.error(`Redis pattern invalidation error for pattern ${pattern}:`, error);
      return 0;
    }
  }

  // Health Check
  async ping(): Promise<boolean> {
    try {
      const client = this.getClient();
      if (!client) throw new Error('Redis client not available');
      const result = await client.ping();
      return result === 'PONG';
    } catch (error) {
      logger.error('Redis ping error:', error);
      return false;
    }
  }

  // Get Connection Info
  getConnectionInfo(): { isConnected: boolean; type: string; nodes: string[] } {
    return {
      isConnected: this.isConnected,
      type: this.cluster ? 'cluster' : 'single',
      nodes: this.cluster ? config.redis.cluster.nodes : [config.redis.url],
    };
  }
}

// Create singleton instance
const redisService = new RedisService();

// Export both the service and a convenience client
export { redisService };
export const getRedisClient = () => redisService.getClient();

// Initialize connection
redisService.connect().catch(error => {
  logger.error('Failed to initialize Redis connection:', error);
  process.exit(1);
});

export default redisService;
