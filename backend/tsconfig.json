{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["node", "jest"], "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"]}